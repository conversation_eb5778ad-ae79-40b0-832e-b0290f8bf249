{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForSubscribe = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForSubscribe\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForUnsubscribe = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnsubscribe\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use admin client to bypass RLS\r\n    const supabaseAdminForReview = createAdminClient();\r\n    const { error: upsertError } = await supabaseAdminForReview\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForDeleteReview = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForDeleteReview\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use admin client to bypass RLS\r\n    const supabaseAdminForLike = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForLike\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForSlug = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForSlug\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use admin client to bypass RLS\r\n    const supabaseAdminForUnlike = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnlike\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForCardData = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForCardData\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForStatus = createAdminClient();\r\n\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabaseAdminForStatus\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAkGsB,0BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { User<PERSON>inus, Loader2, ExternalLink, User, Building2 } from 'lucide-react';\r\nimport { unsubscribeFromBusiness } from '@/lib/actions/interactions';\r\nimport { toast } from 'sonner';\r\nimport { motion } from 'framer-motion';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Shared types for subscription data\r\nexport interface ProfileData {\r\n  id: string;\r\n  name: string | null;\r\n  slug: string | null;\r\n  logo_url?: string | null;\r\n  avatar_url?: string | null;\r\n  city: string | null;\r\n  state: string | null;\r\n  pincode: string | null;\r\n  address_line: string | null;\r\n  type: 'business' | 'customer';\r\n}\r\n\r\nexport interface SubscriptionData {\r\n  id: string;\r\n  profile: ProfileData | null;\r\n}\r\n\r\ninterface SubscriptionCardProps {\r\n  subscriptionId: string;\r\n  profile: ProfileData;\r\n  onUnsubscribeSuccess?: (_subscriptionId: string) => void;\r\n  showUnsubscribe?: boolean;\r\n  variant?: 'default' | 'compact';\r\n}\r\n\r\nexport default function SubscriptionCard({\r\n  subscriptionId,\r\n  profile,\r\n  onUnsubscribeSuccess,\r\n  showUnsubscribe = true,\r\n  variant = 'default',\r\n}: SubscriptionCardProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isHovered, setIsHovered] = useState(false);\r\n\r\n  const handleUnsubscribe = async () => {\r\n    if (!onUnsubscribeSuccess) return;\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      if (!profile.id) {\r\n        toast.error(\"Cannot unsubscribe: Missing profile ID\");\r\n        return;\r\n      }\r\n\r\n      const result = await unsubscribeFromBusiness(profile.id);\r\n\r\n      if (result.success) {\r\n        toast.success(`Unsubscribed from ${profile.name || 'profile'}.`);\r\n        onUnsubscribeSuccess(subscriptionId);\r\n      } else {\r\n        toast.error(`Failed to unsubscribe: ${result.error || 'Unknown error'}`);\r\n      }\r\n    } catch (_error) {\r\n      toast.error(\"An unexpected error occurred\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Format the full address\r\n  const addressParts = [\r\n    profile.address_line,\r\n    profile.city,\r\n    profile.state,\r\n    profile.pincode ? `PIN: ${profile.pincode}` : null\r\n  ].filter(Boolean);\r\n\r\n  const location = addressParts.join(', ');\r\n  const profileUrl = profile.type === 'business' ? `/${profile.slug}` : `/profile/${profile.slug}`;\r\n  const imageUrl = profile.logo_url || profile.avatar_url;\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n      className={cn(\r\n        \"rounded-lg border p-0 overflow-hidden transition-all duration-300\",\r\n        \"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800\",\r\n        isHovered ? \"shadow-md transform -translate-y-1\" : \"shadow-sm\",\r\n        variant === 'compact' && \"max-w-sm\"\r\n      )}\r\n      onMouseEnter={() => setIsHovered(true)}\r\n      onMouseLeave={() => setIsHovered(false)}\r\n    >\r\n      {/* Card with header image background */}\r\n      <div className=\"relative\">\r\n        {/* Decorative header */}\r\n        <div\r\n          className={cn(\r\n            \"w-full bg-gradient-to-r from-blue-500/20 to-[var(--brand-gold)]/20 dark:from-blue-900/30 dark:to-[var(--brand-gold)]/30\",\r\n            variant === 'compact' ? \"h-16\" : \"h-20\"\r\n          )}\r\n        >\r\n          {/* Decorative pattern overlay */}\r\n          <div\r\n            className=\"absolute inset-0 opacity-10 dark:opacity-20 bg-repeat\"\r\n            style={{\r\n              backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n              backgroundSize: \"200px\",\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Avatar - positioned to overlap the header */}\r\n        <div className={cn(\r\n          \"absolute left-4\",\r\n          variant === 'compact' ? \"-bottom-4\" : \"-bottom-6\"\r\n        )}>\r\n          <div className=\"p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800\">\r\n            <Avatar className={cn(\r\n              \"border border-neutral-200 dark:border-neutral-700 shadow-sm\",\r\n              variant === 'compact' ? \"h-12 w-12\" : \"h-16 w-16\"\r\n            )}>\r\n              {imageUrl ? (\r\n                <AvatarImage src={imageUrl} alt={profile.name ?? 'Profile'} />\r\n              ) : null}\r\n              <AvatarFallback className={cn(\r\n                \"bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 text-blue-600 dark:text-blue-300 font-semibold\",\r\n                variant === 'compact' ? \"text-lg\" : \"text-xl\"\r\n              )}>\r\n                {profile.type === 'customer' ? (\r\n                  <User className={variant === 'compact' ? \"h-4 w-4\" : \"h-6 w-6\"} />\r\n                ) : (\r\n                  profile.name?.charAt(0).toUpperCase() ?? 'P'\r\n                )}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Card content */}\r\n      <div className={cn(\r\n        \"px-4 pb-4\",\r\n        variant === 'compact' ? \"pt-6\" : \"pt-8\"\r\n      )}>\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"mb-3\">\r\n            <h3 className={cn(\r\n              \"font-semibold text-neutral-800 dark:text-neutral-200 group flex items-center gap-1\",\r\n              variant === 'compact' ? \"text-base\" : \"text-lg\"\r\n            )}>\r\n              {profile.slug ? (\r\n                <Link\r\n                  href={profileUrl}\r\n                  className=\"hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1\"\r\n                  target=\"_blank\"\r\n                >\r\n                  {profile.name}\r\n                  <ExternalLink className=\"h-3.5 w-3.5 opacity-70\" />\r\n                </Link>\r\n              ) : (\r\n                <span>{profile.name}</span>\r\n              )}\r\n            </h3>\r\n            {location && (\r\n              <p className={cn(\r\n                \"text-neutral-500 dark:text-neutral-400 mt-1 flex items-center\",\r\n                variant === 'compact' ? \"text-xs\" : \"text-sm\"\r\n              )}>\r\n                <span className=\"inline-block h-1 w-1 rounded-full bg-neutral-300 dark:bg-neutral-700 mr-2\"></span>\r\n                {location}\r\n              </p>\r\n            )}\r\n            {profile.type === 'customer' && (\r\n              <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1 flex items-center\">\r\n                <User className=\"h-3 w-3 mr-1\" />\r\n                Customer\r\n              </p>\r\n            )}\r\n            {profile.type === 'business' && (\r\n              <p className=\"text-xs text-green-600 dark:text-green-400 mt-1 flex items-center\">\r\n                <Building2 className=\"h-3 w-3 mr-1\" />\r\n                Business\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Action button */}\r\n          {showUnsubscribe && onUnsubscribeSuccess && (\r\n            <Button\r\n              variant=\"outline\"\r\n              size={variant === 'compact' ? \"sm\" : \"sm\"}\r\n              onClick={handleUnsubscribe}\r\n              disabled={isLoading}\r\n              className={cn(\r\n                \"mt-2 w-full border-neutral-200 dark:border-neutral-700 transition-all duration-200\",\r\n                \"hover:bg-red-50 hover:text-red-600 hover:border-red-200\",\r\n                \"dark:hover:bg-red-950/30 dark:hover:text-red-400 dark:hover:border-red-900/50\"\r\n              )}\r\n            >\r\n              {isLoading ? (\r\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n              ) : (\r\n                <UserMinus className=\"mr-2 h-4 w-4\" />\r\n              )}\r\n              Unsubscribe\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAuCe,SAAS,iBAAiB,EACvC,cAAc,EACd,OAAO,EACP,oBAAoB,EACpB,kBAAkB,IAAI,EACtB,UAAU,SAAS,EACG;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,IAAI,CAAC,sBAAsB;QAE3B,aAAa;QACb,IAAI;YACF,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,EAAE;YAEvD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,kBAAkB,EAAE,QAAQ,IAAI,IAAI,UAAU,CAAC,CAAC;gBAC/D,qBAAqB;YACvB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,KAAK,IAAI,iBAAiB;YACzE;QACF,EAAE,OAAO,QAAQ;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,eAAe;QACnB,QAAQ,YAAY;QACpB,QAAQ,IAAI;QACZ,QAAQ,KAAK;QACb,QAAQ,OAAO,GAAG,CAAC,KAAK,EAAE,QAAQ,OAAO,EAAE,GAAG;KAC/C,CAAC,MAAM,CAAC;IAET,MAAM,WAAW,aAAa,IAAI,CAAC;IACnC,MAAM,aAAa,QAAQ,IAAI,KAAK,aAAa,CAAC,CAAC,EAAE,QAAQ,IAAI,EAAE,GAAG,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;IAChG,MAAM,WAAW,QAAQ,QAAQ,IAAI,QAAQ,UAAU;IAEvD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qEACA,qEACA,YAAY,uCAAuC,aACnD,YAAY,aAAa;QAE3B,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;;0BAGjC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2HACA,YAAY,YAAY,SAAS;kCAInC,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,CAAC,mCAAmC,CAAC;gCACtD,gBAAgB;4BAClB;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,mBACA,YAAY,YAAY,cAAc;kCAEtC,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAClB,+DACA,YAAY,YAAY,cAAc;;oCAErC,yBACC,8OAAC,2HAAA,CAAA,cAAW;wCAAC,KAAK;wCAAU,KAAK,QAAQ,IAAI,IAAI;;;;;+CAC/C;kDACJ,8OAAC,2HAAA,CAAA,iBAAc;wCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAC1B,kIACA,YAAY,YAAY,YAAY;kDAEnC,QAAQ,IAAI,KAAK,2BAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAW,YAAY,YAAY,YAAY;;;;;mDAErD,QAAQ,IAAI,EAAE,OAAO,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrD,8OAAC;gBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,aACA,YAAY,YAAY,SAAS;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACd,sFACA,YAAY,YAAY,cAAc;8CAErC,QAAQ,IAAI,iBACX,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,QAAO;;4CAEN,QAAQ,IAAI;0DACb,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;6DAG1B,8OAAC;kDAAM,QAAQ,IAAI;;;;;;;;;;;gCAGtB,0BACC,8OAAC;oCAAE,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACb,iEACA,YAAY,YAAY,YAAY;;sDAEpC,8OAAC;4CAAK,WAAU;;;;;;wCACf;;;;;;;gCAGJ,QAAQ,IAAI,KAAK,4BAChB,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAIpC,QAAQ,IAAI,KAAK,4BAChB,8OAAC;oCAAE,WAAU;;sDACX,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;wBAO3C,mBAAmB,sCAClB,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAM,YAAY,YAAY,OAAO;4BACrC,SAAS;4BACT,UAAU;4BACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sFACA,2DACA;;gCAGD,0BACC,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCACrB;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionCardSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface SubscriptionCardSkeletonProps {\r\n  index?: number;\r\n  variant?: 'default' | 'compact';\r\n}\r\n\r\nexport default function SubscriptionCardSkeleton({ \r\n  index = 0, \r\n  variant = 'default' \r\n}: SubscriptionCardSkeletonProps) {\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4, delay: index * 0.05 }}\r\n      className={cn(\r\n        \"rounded-lg border p-0 overflow-hidden transition-all duration-300\",\r\n        \"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800\",\r\n        \"shadow-sm\",\r\n        variant === 'compact' && \"max-w-sm\"\r\n      )}\r\n    >\r\n      {/* Card with header image background */}\r\n      <div className=\"relative\">\r\n        {/* Decorative header skeleton */}\r\n        <Skeleton className={cn(\r\n          \"w-full\",\r\n          variant === 'compact' ? \"h-16\" : \"h-20\"\r\n        )} />\r\n\r\n        {/* Avatar - positioned to overlap the header */}\r\n        <div className={cn(\r\n          \"absolute left-4\",\r\n          variant === 'compact' ? \"-bottom-4\" : \"-bottom-6\"\r\n        )}>\r\n          <div className=\"p-1 bg-white dark:bg-black rounded-full border-2 border-white dark:border-neutral-800\">\r\n            <Skeleton className={cn(\r\n              \"rounded-full\",\r\n              variant === 'compact' ? \"h-12 w-12\" : \"h-16 w-16\"\r\n            )} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Card content */}\r\n      <div className={cn(\r\n        \"px-4 pb-4\",\r\n        variant === 'compact' ? \"pt-6\" : \"pt-8\"\r\n      )}>\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"mb-3\">\r\n            {/* Name skeleton */}\r\n            <Skeleton className={cn(\r\n              \"mb-2\",\r\n              variant === 'compact' ? \"h-5 w-32\" : \"h-6 w-40\"\r\n            )} />\r\n            {/* Location skeleton */}\r\n            <Skeleton className={cn(\r\n              \"mt-1\",\r\n              variant === 'compact' ? \"h-3 w-24\" : \"h-4 w-32\"\r\n            )} />\r\n          </div>\r\n\r\n          {/* Action button skeleton */}\r\n          <Skeleton className={cn(\r\n            \"w-full mt-2 rounded-md\",\r\n            variant === 'compact' ? \"h-8\" : \"h-9\"\r\n          )} />\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\nexport function SubscriptionListSkeleton({ \r\n  variant = 'default',\r\n  count = 6 \r\n}: { \r\n  variant?: 'default' | 'compact';\r\n  count?: number;\r\n}) {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n      {Array.from({ length: count }).map((_, index) => (\r\n        <SubscriptionCardSkeleton \r\n          key={index} \r\n          index={index} \r\n          variant={variant}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,yBAAyB,EAC/C,QAAQ,CAAC,EACT,UAAU,SAAS,EACW;IAC9B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;QACjD,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qEACA,qEACA,aACA,YAAY,aAAa;;0BAI3B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACpB,UACA,YAAY,YAAY,SAAS;;;;;;kCAInC,8OAAC;wBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,mBACA,YAAY,YAAY,cAAc;kCAEtC,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACpB,gBACA,YAAY,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;0BAO9C,8OAAC;gBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACf,aACA,YAAY,YAAY,SAAS;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACpB,QACA,YAAY,YAAY,aAAa;;;;;;8CAGvC,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACpB,QACA,YAAY,YAAY,aAAa;;;;;;;;;;;;sCAKzC,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACpB,0BACA,YAAY,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAM5C;AAEO,SAAS,yBAAyB,EACvC,UAAU,SAAS,EACnB,QAAQ,CAAC,EAIV;IACC,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;gBAEC,OAAO;gBACP,SAAS;eAFJ;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionSearch.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface SubscriptionSearchProps {\r\n  onSearch: (_searchTerm: string) => void;\r\n  initialSearchTerm?: string;\r\n  className?: string;\r\n  placeholder?: string;\r\n}\r\n\r\nexport default function SubscriptionSearch({\r\n  onSearch,\r\n  initialSearchTerm = \"\",\r\n  className,\r\n  placeholder = \"Search...\",\r\n}: SubscriptionSearchProps) {\r\n  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);\r\n\r\n  // Update search term when initialSearchTerm changes (e.g., when navigating back)\r\n  useEffect(() => {\r\n    setSearchTerm(initialSearchTerm);\r\n  }, [initialSearchTerm]);\r\n\r\n  // Handle input change\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchTerm(e.target.value);\r\n  };\r\n\r\n  // Clear search\r\n  const handleClearSearch = () => {\r\n    setSearchTerm(\"\");\r\n    if (typeof onSearch === 'function') {\r\n      onSearch(\"\");\r\n    }\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (typeof onSearch === 'function') {\r\n      onSearch(searchTerm);\r\n    }\r\n  };\r\n\r\n  // Handle Enter key press for immediate search\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      if (typeof onSearch === 'function') {\r\n        onSearch(searchTerm);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={handleSubmit} className={cn(\"relative w-full\", className)}>\r\n      <div className=\"relative\">\r\n        <Search className=\"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400\" />\r\n        <Input\r\n          type=\"text\"\r\n          placeholder={placeholder}\r\n          value={searchTerm}\r\n          onChange={handleSearchChange}\r\n          onKeyDown={handleKeyDown}\r\n          className=\"pl-10 pr-10 h-10 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600\"\r\n        />\r\n        {searchTerm && (\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={handleClearSearch}\r\n            className=\"absolute right-2 top-1/2 -translate-y-1/2 h-6 w-6 p-0 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300\"\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n            <span className=\"sr-only\">Clear search</span>\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAee,SAAS,mBAAmB,EACzC,QAAQ,EACR,oBAAoB,EAAE,EACtB,SAAS,EACT,cAAc,WAAW,EACD;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,iFAAiF;IACjF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;KAAkB;IAEtB,sBAAsB;IACtB,MAAM,qBAAqB,CAAC;QAC1B,cAAc,EAAE,MAAM,CAAC,KAAK;IAC9B;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB,cAAc;QACd,IAAI,OAAO,aAAa,YAAY;YAClC,SAAS;QACX;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,OAAO,aAAa,YAAY;YAClC,SAAS;QACX;IACF;IAEA,8CAA8C;IAC9C,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,OAAO,aAAa,YAAY;gBAClC,SAAS;YACX;QACF;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;kBAC7D,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,8OAAC,0HAAA,CAAA,QAAK;oBACJ,MAAK;oBACL,aAAa;oBACb,OAAO;oBACP,UAAU;oBACV,WAAW;oBACX,WAAU;;;;;;gBAEX,4BACC,8OAAC,2HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;sCACb,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionPagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface SubscriptionPaginationProps {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (_page: number) => void;\r\n  className?: string;\r\n}\r\n\r\nexport default function SubscriptionPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  className,\r\n}: SubscriptionPaginationProps) {\r\n  // Don't render pagination if there's only one page\r\n  if (totalPages <= 1) return null;\r\n\r\n  // Generate page numbers to display\r\n  const getPageNumbers = () => {\r\n    const pages = [];\r\n    const maxPagesToShow = 5;\r\n\r\n    if (totalPages <= maxPagesToShow) {\r\n      // Show all pages if total is less than max\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        pages.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      pages.push(1);\r\n\r\n      // Calculate start and end of middle section\r\n      let startPage = Math.max(2, currentPage - 1);\r\n      let endPage = Math.min(totalPages - 1, currentPage + 1);\r\n\r\n      // Adjust if we're near the beginning\r\n      if (currentPage <= 3) {\r\n        endPage = Math.min(totalPages - 1, 4);\r\n      }\r\n\r\n      // Adjust if we're near the end\r\n      if (currentPage >= totalPages - 2) {\r\n        startPage = Math.max(2, totalPages - 3);\r\n      }\r\n\r\n      // Add ellipsis if needed\r\n      if (startPage > 2) {\r\n        pages.push(-1); // -1 represents ellipsis\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = startPage; i <= endPage; i++) {\r\n        pages.push(i);\r\n      }\r\n\r\n      // Add ellipsis if needed\r\n      if (endPage < totalPages - 1) {\r\n        pages.push(-2); // -2 represents ellipsis\r\n      }\r\n\r\n      // Always include last page\r\n      pages.push(totalPages);\r\n    }\r\n\r\n    return pages;\r\n  };\r\n\r\n  const pageNumbers = getPageNumbers();\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center justify-center space-x-1\", className)}>\r\n      {/* Previous button */}\r\n      <Button\r\n        variant=\"outline\"\r\n        size=\"icon\"\r\n        onClick={() => onPageChange(currentPage - 1)}\r\n        disabled={currentPage === 1}\r\n        className=\"h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800\"\r\n      >\r\n        <ChevronLeft className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Previous page</span>\r\n      </Button>\r\n\r\n      {/* Page numbers */}\r\n      {pageNumbers.map((page, _index) => {\r\n        if (page < 0) {\r\n          // Render ellipsis\r\n          return (\r\n            <span key={`ellipsis-${_index}`} className=\"px-2 text-neutral-500 dark:text-neutral-400\">\r\n              ...\r\n            </span>\r\n          );\r\n        }\r\n\r\n        return (\r\n          <Button\r\n            key={page}\r\n            variant={currentPage === page ? \"default\" : \"outline\"}\r\n            size=\"sm\"\r\n            onClick={() => onPageChange(page)}\r\n            className={cn(\r\n              \"h-8 w-8 p-0\",\r\n              currentPage === page\r\n                ? \"bg-blue-500 hover:bg-blue-600 text-white border-transparent\"\r\n                : \"border-neutral-200 dark:border-neutral-800\"\r\n            )}\r\n          >\r\n            {page}\r\n          </Button>\r\n        );\r\n      })}\r\n\r\n      {/* Next button */}\r\n      <Button\r\n        variant=\"outline\"\r\n        size=\"icon\"\r\n        onClick={() => onPageChange(currentPage + 1)}\r\n        disabled={currentPage === totalPages}\r\n        className=\"h-8 w-8 p-0 border-neutral-200 dark:border-neutral-800\"\r\n      >\r\n        <ChevronRight className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Next page</span>\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAae,SAAS,uBAAuB,EAC7C,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACmB;IAC5B,mDAAmD;IACnD,IAAI,cAAc,GAAG,OAAO;IAE5B,mCAAmC;IACnC,MAAM,iBAAiB;QACrB,MAAM,QAAQ,EAAE;QAChB,MAAM,iBAAiB;QAEvB,IAAI,cAAc,gBAAgB;YAChC,2CAA2C;YAC3C,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,MAAM,IAAI,CAAC;YACb;QACF,OAAO;YACL,4BAA4B;YAC5B,MAAM,IAAI,CAAC;YAEX,4CAA4C;YAC5C,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,cAAc;YAC1C,IAAI,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc;YAErD,qCAAqC;YACrC,IAAI,eAAe,GAAG;gBACpB,UAAU,KAAK,GAAG,CAAC,aAAa,GAAG;YACrC;YAEA,+BAA+B;YAC/B,IAAI,eAAe,aAAa,GAAG;gBACjC,YAAY,KAAK,GAAG,CAAC,GAAG,aAAa;YACvC;YAEA,yBAAyB;YACzB,IAAI,YAAY,GAAG;gBACjB,MAAM,IAAI,CAAC,CAAC,IAAI,yBAAyB;YAC3C;YAEA,mBAAmB;YACnB,IAAK,IAAI,IAAI,WAAW,KAAK,SAAS,IAAK;gBACzC,MAAM,IAAI,CAAC;YACb;YAEA,yBAAyB;YACzB,IAAI,UAAU,aAAa,GAAG;gBAC5B,MAAM,IAAI,CAAC,CAAC,IAAI,yBAAyB;YAC3C;YAEA,2BAA2B;YAC3B,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;;0BAE/D,8OAAC,2HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,gBAAgB;gBAC1B,WAAU;;kCAEV,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;YAI3B,YAAY,GAAG,CAAC,CAAC,MAAM;gBACtB,IAAI,OAAO,GAAG;oBACZ,kBAAkB;oBAClB,qBACE,8OAAC;wBAAgC,WAAU;kCAA8C;uBAA9E,CAAC,SAAS,EAAE,QAAQ;;;;;gBAInC;gBAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;oBAEL,SAAS,gBAAgB,OAAO,YAAY;oBAC5C,MAAK;oBACL,SAAS,IAAM,aAAa;oBAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,eACA,gBAAgB,OACZ,gEACA;8BAGL;mBAXI;;;;;YAcX;0BAGA,8OAAC,2HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,gBAAgB;gBAC1B,WAAU;;kCAEV,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;kCACxB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAIlC", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/SubscriptionList.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Compass } from 'lucide-react';\r\nimport SubscriptionCard, { SubscriptionData } from './SubscriptionCard';\r\n\r\ninterface SubscriptionListProps {\r\n  initialSubscriptions: SubscriptionData[];\r\n  onUnsubscribeSuccess?: (_subscriptionId: string) => void;\r\n  showUnsubscribe?: boolean;\r\n  variant?: 'default' | 'compact';\r\n  emptyMessage?: string;\r\n  emptyDescription?: string;\r\n  showDiscoverButton?: boolean;\r\n}\r\n\r\nexport default function SubscriptionList({\r\n  initialSubscriptions,\r\n  onUnsubscribeSuccess,\r\n  showUnsubscribe = true,\r\n  variant = 'default',\r\n  emptyMessage = \"No subscriptions found.\",\r\n  emptyDescription = \"Subscribe to profiles to see them here.\",\r\n  showDiscoverButton = false\r\n}: SubscriptionListProps) {\r\n  const [subscriptions, setSubscriptions] = useState(initialSubscriptions);\r\n\r\n  // Update subscriptions when initialSubscriptions changes\r\n  useEffect(() => {\r\n    setSubscriptions(initialSubscriptions);\r\n  }, [initialSubscriptions]);\r\n\r\n  const handleUnsubscribeSuccess = (subscriptionIdToRemove: string) => {\r\n    setSubscriptions(currentSubscriptions =>\r\n      currentSubscriptions.filter(sub => sub.id !== subscriptionIdToRemove)\r\n    );\r\n\r\n    // Call parent callback if provided\r\n    if (onUnsubscribeSuccess) {\r\n      onUnsubscribeSuccess(subscriptionIdToRemove);\r\n    }\r\n  };\r\n\r\n  if (subscriptions.length === 0) {\r\n    return (\r\n      <div className=\"rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-black/50 p-8 text-center\">\r\n        <p className=\"text-neutral-600 dark:text-neutral-400\">{emptyMessage}</p>\r\n        <p className=\"text-sm text-neutral-500 dark:text-neutral-500 mt-2\">\r\n          {emptyDescription}\r\n        </p>\r\n        {showDiscoverButton && (\r\n          <div className=\"mt-6\">\r\n            <Link href=\"/discover\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"inline-flex items-center gap-2 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 dark:hover:bg-blue-950/30 dark:hover:text-blue-400 dark:hover:border-blue-900/50 transition-all duration-200\"\r\n              >\r\n                <Compass className=\"h-4 w-4\" />\r\n                Discover Businesses Nearby\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n      {subscriptions.map((sub, _index) => {\r\n        const profile = sub.profile;\r\n\r\n        if (!profile) {\r\n          return null; // Skip items with missing profiles\r\n        }\r\n\r\n        return (\r\n          <SubscriptionCard\r\n            key={sub.id}\r\n            subscriptionId={sub.id}\r\n            profile={profile}\r\n            onUnsubscribeSuccess={showUnsubscribe ? handleUnsubscribeSuccess : undefined}\r\n            showUnsubscribe={showUnsubscribe}\r\n            variant={variant}\r\n          />\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAkBe,SAAS,iBAAiB,EACvC,oBAAoB,EACpB,oBAAoB,EACpB,kBAAkB,IAAI,EACtB,UAAU,SAAS,EACnB,eAAe,yBAAyB,EACxC,mBAAmB,yCAAyC,EAC5D,qBAAqB,KAAK,EACJ;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;IACnB,GAAG;QAAC;KAAqB;IAEzB,MAAM,2BAA2B,CAAC;QAChC,iBAAiB,CAAA,uBACf,qBAAqB,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAGhD,mCAAmC;QACnC,IAAI,sBAAsB;YACxB,qBAAqB;QACvB;IACF;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAA0C;;;;;;8BACvD,8OAAC;oBAAE,WAAU;8BACV;;;;;;gBAEF,oCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAY,QAAO;wBAAS,KAAI;kCACzC,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;IAQ7C;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,KAAK;YACvB,MAAM,UAAU,IAAI,OAAO;YAE3B,IAAI,CAAC,SAAS;gBACZ,OAAO,MAAM,mCAAmC;YAClD;YAEA,qBACE,8OAAC,iKAAA,CAAA,UAAgB;gBAEf,gBAAgB,IAAI,EAAE;gBACtB,SAAS;gBACT,sBAAsB,kBAAkB,2BAA2B;gBACnE,iBAAiB;gBACjB,SAAS;eALJ,IAAI,EAAE;;;;;QAQjB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/subscriptions/index.ts"], "sourcesContent": ["// Export all shared subscription components\r\nexport { default as SubscriptionCard } from './SubscriptionCard';\r\nexport { default as SubscriptionCardSkeleton, SubscriptionListSkeleton } from './SubscriptionCardSkeleton';\r\nexport { default as SubscriptionSearch } from './SubscriptionSearch';\r\nexport { default as SubscriptionPagination } from './SubscriptionPagination';\r\nexport { default as SubscriptionList } from './SubscriptionList';\r\n\r\n// Export types\r\nexport type { ProfileData, SubscriptionData } from './SubscriptionCard';\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;AAC5C;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/subscriptions/SubscriptionListClient.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useMemo } from 'react';\r\nimport { SubscriptionList, SubscriptionData } from '@/app/components/shared/subscriptions';\r\nimport { SubscriptionWithProfile } from './actions';\r\n\r\ninterface SubscriptionListClientProps {\r\n  initialSubscriptions: SubscriptionWithProfile[];\r\n}\r\n\r\nexport default function SubscriptionListClient({ initialSubscriptions }: SubscriptionListClientProps) {\r\n  // Transform the data to match the shared component interface\r\n  const transformedSubscriptions: SubscriptionData[] = useMemo(() => {\r\n    return initialSubscriptions.map(sub => ({\r\n      id: sub.id,\r\n      profile: sub.business_profiles ? {\r\n        id: sub.business_profiles.id,\r\n        name: sub.business_profiles.business_name,\r\n        slug: sub.business_profiles.business_slug,\r\n        logo_url: sub.business_profiles.logo_url,\r\n        city: sub.business_profiles.city,\r\n        state: sub.business_profiles.state,\r\n        pincode: sub.business_profiles.pincode,\r\n        address_line: sub.business_profiles.address_line,\r\n        type: 'business' as const,\r\n      } : null\r\n    })).filter(sub => sub.profile !== null) as SubscriptionData[];\r\n  }, [initialSubscriptions]);\r\n\r\n  return (\r\n    <SubscriptionList\r\n      initialSubscriptions={transformedSubscriptions}\r\n      showUnsubscribe={true}\r\n      emptyMessage=\"You haven't subscribed to any businesses yet.\"\r\n      emptyDescription=\"Subscribe to businesses to receive updates and notifications.\"\r\n      showDiscoverButton={true}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAUe,SAAS,uBAAuB,EAAE,oBAAoB,EAA+B;IAClG,6DAA6D;IAC7D,MAAM,2BAA+C,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3D,OAAO,qBAAqB,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtC,IAAI,IAAI,EAAE;gBACV,SAAS,IAAI,iBAAiB,GAAG;oBAC/B,IAAI,IAAI,iBAAiB,CAAC,EAAE;oBAC5B,MAAM,IAAI,iBAAiB,CAAC,aAAa;oBACzC,MAAM,IAAI,iBAAiB,CAAC,aAAa;oBACzC,UAAU,IAAI,iBAAiB,CAAC,QAAQ;oBACxC,MAAM,IAAI,iBAAiB,CAAC,IAAI;oBAChC,OAAO,IAAI,iBAAiB,CAAC,KAAK;oBAClC,SAAS,IAAI,iBAAiB,CAAC,OAAO;oBACtC,cAAc,IAAI,iBAAiB,CAAC,YAAY;oBAChD,MAAM;gBACR,IAAI;YACN,CAAC,GAAG,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK;IACpC,GAAG;QAAC;KAAqB;IAEzB,qBACE,8OAAC,gNAAA,CAAA,mBAAgB;QACf,sBAAsB;QACtB,iBAAiB;QACjB,cAAa;QACb,kBAAiB;QACjB,oBAAoB;;;;;;AAG1B", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/subscriptions/components/SubscriptionsPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { Bell } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport SubscriptionListClient from \"../SubscriptionListClient\";\r\nimport { SubscriptionSearch, SubscriptionPagination, SubscriptionListSkeleton } from \"@/app/components/shared/subscriptions\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport { SubscriptionWithProfile } from \"../actions\";\r\n\r\ninterface SubscriptionsPageClientProps {\r\n  initialSubscriptions: SubscriptionWithProfile[];\r\n  totalCount: number;\r\n  currentPage: number;\r\n  searchTerm: string;\r\n}\r\n\r\nexport default function SubscriptionsPageClient({\r\n  initialSubscriptions,\r\n  totalCount,\r\n  currentPage,\r\n  searchTerm,\r\n}: SubscriptionsPageClientProps) {\r\n  const router = useRouter();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  // Calculate total pages\r\n  const itemsPerPage = 10;\r\n  const totalPages = Math.max(1, Math.ceil(totalCount / itemsPerPage));\r\n\r\n  // Reset loading state when component receives new data\r\n  useEffect(() => {\r\n    setIsLoading(false);\r\n  }, [initialSubscriptions]);\r\n\r\n  // Handle search - use useCallback to prevent infinite re-renders\r\n  const handleSearch = useCallback((term: string) => {\r\n    setIsLoading(true); // Show loading state\r\n    const params = new URLSearchParams();\r\n    if (term) {\r\n      params.set('search', term);\r\n    }\r\n    params.set('page', '1'); // Reset to page 1 on new search\r\n    router.push(`/dashboard/customer/subscriptions?${params.toString()}`);\r\n  }, [router]);\r\n\r\n  // Handle page change - use useCallback to prevent infinite re-renders\r\n  const handlePageChange = useCallback((page: number) => {\r\n    setIsLoading(true); // Show loading state\r\n    const params = new URLSearchParams();\r\n    if (searchTerm) {\r\n      params.set('search', searchTerm);\r\n    }\r\n    params.set('page', page.toString());\r\n    router.push(`/dashboard/customer/subscriptions?${params.toString()}`);\r\n  }, [router, searchTerm]);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header Section with proper layout */}\r\n      <div className=\"flex flex-col gap-6\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n          <div className=\"flex items-center gap-4\">\r\n            <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n              <Bell className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n            <div>\r\n              <h1 className=\"text-2xl font-bold text-foreground\">\r\n                Subscribed Businesses\r\n              </h1>\r\n              <p className=\"text-muted-foreground mt-1\">\r\n                {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'} you&apos;re following for updates\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Search Section - aligned to the right on desktop */}\r\n          <div className=\"w-full sm:w-80\">\r\n            <SubscriptionSearch\r\n              onSearch={handleSearch}\r\n              initialSearchTerm={searchTerm}\r\n              placeholder=\"Search businesses...\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Results count */}\r\n        {searchTerm && !isLoading && (\r\n          <div className=\"text-sm text-muted-foreground border-l-4 border-primary pl-4\">\r\n            Found {formatIndianNumberShort(totalCount)} {totalCount === 1 ? 'business' : 'businesses'}\r\n            {searchTerm ? ` matching \"${searchTerm}\"` : ''}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Content Section */}\r\n      <div className=\"space-y-6\">\r\n        {/* Show skeleton loader when loading */}\r\n        {isLoading ? (\r\n          <SubscriptionListSkeleton />\r\n        ) : (\r\n          <>\r\n            {/* Subscription List */}\r\n            <SubscriptionListClient initialSubscriptions={initialSubscriptions} />\r\n\r\n            {/* Pagination */}\r\n            {totalPages > 1 && (\r\n              <div className=\"flex justify-center pt-6\">\r\n                <SubscriptionPagination\r\n                  currentPage={currentPage}\r\n                  totalPages={totalPages}\r\n                  onPageChange={handlePageChange}\r\n                />\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;;AAiBe,SAAS,wBAAwB,EAC9C,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,UAAU,EACmB;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,wBAAwB;IACxB,MAAM,eAAe;IACrB,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,IAAI,CAAC,aAAa;IAEtD,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG;QAAC;KAAqB;IAEzB,iEAAiE;IACjE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,aAAa,OAAO,qBAAqB;QACzC,MAAM,SAAS,IAAI;QACnB,IAAI,MAAM;YACR,OAAO,GAAG,CAAC,UAAU;QACvB;QACA,OAAO,GAAG,CAAC,QAAQ,MAAM,gCAAgC;QACzD,OAAO,IAAI,CAAC,CAAC,kCAAkC,EAAE,OAAO,QAAQ,IAAI;IACtE,GAAG;QAAC;KAAO;IAEX,sEAAsE;IACtE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,aAAa,OAAO,qBAAqB;QACzC,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY;YACd,OAAO,GAAG,CAAC,UAAU;QACvB;QACA,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ;QAChC,OAAO,IAAI,CAAC,CAAC,kCAAkC,EAAE,OAAO,QAAQ,IAAI;IACtE,GAAG;QAAC;QAAQ;KAAW;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DAGnD,8OAAC;gDAAE,WAAU;;oDACV,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;oDAAY;oDAAE,eAAe,IAAI,aAAa;oDAAa;;;;;;;;;;;;;;;;;;;0CAM1F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oNAAA,CAAA,qBAAkB;oCACjB,UAAU;oCACV,mBAAmB;oCACnB,aAAY;;;;;;;;;;;;;;;;;oBAMjB,cAAc,CAAC,2BACd,8OAAC;wBAAI,WAAU;;4BAA+D;4BACrE,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;4BAAY;4BAAE,eAAe,IAAI,aAAa;4BAC5E,aAAa,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC,GAAG;;;;;;;;;;;;;0BAMlD,8OAAC;gBAAI,WAAU;0BAEZ,0BACC,8OAAC,yKAAA,CAAA,2BAAwB;;;;yCAEzB;;sCAEE,8OAAC,yLAAA,CAAA,UAAsB;4BAAC,sBAAsB;;;;;;wBAG7C,aAAa,mBACZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4NAAA,CAAA,yBAAsB;gCACrB,aAAa;gCACb,YAAY;gCACZ,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAShC", "debugId": null}}]}