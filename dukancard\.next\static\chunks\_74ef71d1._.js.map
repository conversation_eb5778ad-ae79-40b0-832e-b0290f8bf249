{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/components/CustomerAnimatedMetricCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { LucideIcon } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\ninterface CustomerAnimatedMetricCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  icon: LucideIcon;\r\n  description: string;\r\n  color: \"blue\" | \"indigo\" | \"purple\" | \"rose\" | \"red\" | \"yellow\" | \"brand\";\r\n  href?: string;\r\n}\r\n\r\nexport default function CustomerAnimatedMetricCard({\r\n  title,\r\n  value,\r\n  icon: Icon,\r\n  description,\r\n  color,\r\n  href,\r\n}: CustomerAnimatedMetricCardProps) {\r\n  // Define modern color variants without glow effects\r\n  const colorVariants = {\r\n    blue: {\r\n      iconBg: \"bg-blue-50 dark:bg-blue-950/50\",\r\n      iconColor: \"text-blue-600 dark:text-blue-400\",\r\n      borderColor: \"border-blue-200/60 dark:border-blue-800/60\",\r\n      hoverBorder: \"hover:border-blue-300 dark:hover:border-blue-700\",\r\n      buttonColor: \"text-blue-600 dark:text-blue-400\",\r\n      buttonBg: \"bg-blue-50 dark:bg-blue-950/50\",\r\n      buttonHover: \"hover:bg-blue-100 dark:hover:bg-blue-900/50\",\r\n    },\r\n    indigo: {\r\n      iconBg: \"bg-indigo-50 dark:bg-indigo-950/50\",\r\n      iconColor: \"text-indigo-600 dark:text-indigo-400\",\r\n      borderColor: \"border-indigo-200/60 dark:border-indigo-800/60\",\r\n      hoverBorder: \"hover:border-indigo-300 dark:hover:border-indigo-700\",\r\n      buttonColor: \"text-indigo-600 dark:text-indigo-400\",\r\n      buttonBg: \"bg-indigo-50 dark:bg-indigo-950/50\",\r\n      buttonHover: \"hover:bg-indigo-100 dark:hover:bg-indigo-900/50\",\r\n    },\r\n    purple: {\r\n      iconBg: \"bg-purple-50 dark:bg-purple-950/50\",\r\n      iconColor: \"text-purple-600 dark:text-purple-400\",\r\n      borderColor: \"border-purple-200/60 dark:border-purple-800/60\",\r\n      hoverBorder: \"hover:border-purple-300 dark:hover:border-purple-700\",\r\n      buttonColor: \"text-purple-600 dark:text-purple-400\",\r\n      buttonBg: \"bg-purple-50 dark:bg-purple-950/50\",\r\n      buttonHover: \"hover:bg-purple-100 dark:hover:bg-purple-900/50\",\r\n    },\r\n    rose: {\r\n      iconBg: \"bg-rose-50 dark:bg-rose-950/50\",\r\n      iconColor: \"text-rose-600 dark:text-rose-400\",\r\n      borderColor: \"border-rose-200/60 dark:border-rose-800/60\",\r\n      hoverBorder: \"hover:border-rose-300 dark:hover:border-rose-700\",\r\n      buttonColor: \"text-rose-600 dark:text-rose-400\",\r\n      buttonBg: \"bg-rose-50 dark:bg-rose-950/50\",\r\n      buttonHover: \"hover:bg-rose-100 dark:hover:bg-rose-900/50\",\r\n    },\r\n    red: {\r\n      iconBg: \"bg-red-50 dark:bg-red-950/50\",\r\n      iconColor: \"text-red-600 dark:text-red-400\",\r\n      borderColor: \"border-red-200/60 dark:border-red-800/60\",\r\n      hoverBorder: \"hover:border-red-300 dark:hover:border-red-700\",\r\n      buttonColor: \"text-red-600 dark:text-red-400\",\r\n      buttonBg: \"bg-red-50 dark:bg-red-950/50\",\r\n      buttonHover: \"hover:bg-red-100 dark:hover:bg-red-900/50\",\r\n    },\r\n    yellow: {\r\n      iconBg: \"bg-yellow-50 dark:bg-yellow-950/50\",\r\n      iconColor: \"text-yellow-600 dark:text-yellow-400\",\r\n      borderColor: \"border-yellow-200/60 dark:border-yellow-800/60\",\r\n      hoverBorder: \"hover:border-yellow-300 dark:hover:border-yellow-700\",\r\n      buttonColor: \"text-yellow-600 dark:text-yellow-400\",\r\n      buttonBg: \"bg-yellow-50 dark:bg-yellow-950/50\",\r\n      buttonHover: \"hover:bg-yellow-100 dark:hover:bg-yellow-900/50\",\r\n    },\r\n    brand: {\r\n      iconBg: \"bg-amber-50 dark:bg-amber-950/50\",\r\n      iconColor: \"text-amber-600 dark:text-amber-400\",\r\n      borderColor: \"border-amber-200/60 dark:border-amber-800/60\",\r\n      hoverBorder: \"hover:border-amber-300 dark:hover:border-amber-700\",\r\n      buttonColor: \"text-amber-600 dark:text-amber-400\",\r\n      buttonBg: \"bg-amber-50 dark:bg-amber-950/50\",\r\n      buttonHover: \"hover:bg-amber-100 dark:hover:bg-amber-900/50\",\r\n    },\r\n  };\r\n\r\n  const selectedColor = colorVariants[color];\r\n\r\n  const cardContent = (\r\n    <>\r\n      {/* Content */}\r\n      <div className=\"flex flex-col items-center text-center space-y-3\">\r\n        {/* Icon */}\r\n        <div className={`p-3 rounded-xl ${selectedColor.iconBg} transition-all duration-200 group-hover:scale-105`}>\r\n          <Icon className={`w-6 h-6 ${selectedColor.iconColor}`} />\r\n        </div>\r\n\r\n        {/* Value */}\r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-2xl font-bold text-foreground\">\r\n            {value}\r\n          </div>\r\n          <div className=\"text-sm font-medium text-muted-foreground\">\r\n            {title}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Description */}\r\n        <p className=\"text-xs text-muted-foreground\">\r\n          {description}\r\n        </p>\r\n\r\n        {/* Interactive Button - only show for cards with href */}\r\n        {href && (\r\n          <div className=\"mt-4\">\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className={`\r\n                w-full text-xs font-medium transition-all duration-200\r\n                ${selectedColor.buttonColor}\r\n                ${selectedColor.buttonBg} ${selectedColor.buttonHover}\r\n                border-current/20 hover:border-current/40\r\n              `}\r\n            >\r\n              <Link href={href}>\r\n                View {title}\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <div className={`\r\n      group relative overflow-hidden rounded-xl p-6\r\n      bg-card border ${selectedColor.borderColor} ${selectedColor.hoverBorder}\r\n      shadow-sm hover:shadow-md\r\n      transition-all duration-200\r\n      cursor-default\r\n    `}>\r\n      {cardContent}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAee,SAAS,2BAA2B,EACjD,KAAK,EACL,KAAK,EACL,MAAM,IAAI,EACV,WAAW,EACX,KAAK,EACL,IAAI,EAC4B;IAChC,oDAAoD;IACpD,MAAM,gBAAgB;QACpB,MAAM;YACJ,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;QACA,QAAQ;YACN,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;QACA,QAAQ;YACN,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;QACA,MAAM;Y<PERSON><PERSON>,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;QACA,KAAK;YACH,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;QACA,QAAQ;YACN,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;QACA,OAAO;YACL,QAAQ;YACR,WAAW;YACX,aAAa;YACb,aAAa;YACb,aAAa;YACb,UAAU;YACV,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,aAAa,CAAC,MAAM;IAE1C,MAAM,4BACJ;kBAEE,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAW,CAAC,eAAe,EAAE,cAAc,MAAM,CAAC,kDAAkD,CAAC;8BACxG,cAAA,6LAAC;wBAAK,WAAW,CAAC,QAAQ,EAAE,cAAc,SAAS,EAAE;;;;;;;;;;;8BAIvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAEH,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAKL,6LAAC;oBAAE,WAAU;8BACV;;;;;;gBAIF,sBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,OAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAC;;gBAEV,EAAE,cAAc,WAAW,CAAC;gBAC5B,EAAE,cAAc,QAAQ,CAAC,CAAC,EAAE,cAAc,WAAW,CAAC;;cAExD,CAAC;kCAED,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM;;gCAAM;gCACV;;;;;;;;;;;;;;;;;;;;;;;;IASpB,qBACE,6LAAC;QAAI,WAAW,CAAC;;qBAEA,EAAE,cAAc,WAAW,CAAC,CAAC,EAAE,cAAc,WAAW,CAAC;;;;IAI1E,CAAC;kBACE;;;;;;AAGP;KAxIwB", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/components/CustomerMetricsOverview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Star, MessageSquare, Users, Heart } from \"lucide-react\";\r\nimport CustomerAnimatedMetricCard from \"./CustomerAnimatedMetricCard\";\r\n\r\ninterface CustomerMetricsOverviewProps {\r\n  initialReviewCount: number;\r\n  initialSubscriptionCount: number;\r\n  initialLikesCount: number;\r\n  userId: string;\r\n}\r\n\r\nexport default function CustomerMetricsOverview({\r\n  initialReviewCount,\r\n  initialSubscriptionCount,\r\n  initialLikesCount,\r\n  userId: _userId,\r\n}: CustomerMetricsOverviewProps) {\r\n  // Since real-time is not enabled for these tables, we'll use the initial values\r\n  const reviewCount = initialReviewCount;\r\n  const subscriptionCount = initialSubscriptionCount;\r\n  const likesCount = initialLikesCount;\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"space-y-6\"\r\n    >\r\n      {/* Activity Score - Above main metrics */}\r\n      <div className=\"grid grid-cols-1 gap-4\">\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Activity Score\"\r\n          value={reviewCount + subscriptionCount * 2 + likesCount}\r\n          icon={MessageSquare}\r\n          description=\"Your engagement level\"\r\n          color=\"brand\"\r\n        />\r\n      </div>\r\n\r\n      {/* Customer Stats Section - 3 columns: Likes, Subscriptions, Reviews */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n        {/* Likes Card */}\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Likes\"\r\n          value={likesCount}\r\n          icon={Heart}\r\n          description=\"Businesses you've liked\"\r\n          color=\"red\"\r\n          href=\"/dashboard/customer/likes\"\r\n        />\r\n\r\n        {/* Subscriptions Card */}\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Followers\"\r\n          value={subscriptionCount}\r\n          icon={Users}\r\n          description=\"Businesses you're following\"\r\n          color=\"blue\"\r\n          href=\"/dashboard/customer/subscriptions\"\r\n        />\r\n\r\n        {/* Reviews Card */}\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Rating\"\r\n          value={reviewCount}\r\n          icon={Star}\r\n          description=\"Reviews you've left for businesses\"\r\n          color=\"yellow\"\r\n          href=\"/dashboard/customer/reviews\"\r\n        />\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAae,SAAS,wBAAwB,EAC9C,kBAAkB,EAClB,wBAAwB,EACxB,iBAAiB,EACjB,QAAQ,OAAO,EACc;IAC7B,gFAAgF;IAChF,MAAM,cAAc;IACpB,MAAM,oBAAoB;IAC1B,MAAM,aAAa;IAEnB,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,UAA0B;oBACzB,OAAM;oBACN,OAAO,cAAc,oBAAoB,IAAI;oBAC7C,MAAM,2NAAA,CAAA,gBAAa;oBACnB,aAAY;oBACZ,OAAM;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,UAA0B;wBACzB,OAAM;wBACN,OAAO;wBACP,MAAM,uMAAA,CAAA,QAAK;wBACX,aAAY;wBACZ,OAAM;wBACN,MAAK;;;;;;kCAIP,6LAAC,6LAAA,CAAA,UAA0B;wBACzB,OAAM;wBACN,OAAO;wBACP,MAAM,uMAAA,CAAA,QAAK;wBACX,aAAY;wBACZ,OAAM;wBACN,MAAK;;;;;;kCAIP,6LAAC,6LAAA,CAAA,UAA0B;wBACzB,OAAM;wBACN,OAAO;wBACP,MAAM,qMAAA,CAAA,OAAI;wBACV,aAAY;wBACZ,OAAM;wBACN,MAAK;;;;;;;;;;;;;;;;;;AAKf;KA1EwB", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/components/CustomerDashboardClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { User, Settings } from \"lucide-react\";\r\n\r\nimport CustomerMetricsOverview from \"./CustomerMetricsOverview\";\r\n\r\ninterface CustomerDashboardClientProps {\r\n  customerName: string;\r\n  _customerEmail?: string;\r\n  userId: string;\r\n  initialReviewCount: number;\r\n  initialSubscriptionCount: number;\r\n  initialLikesCount: number;\r\n}\r\n\r\nexport default function CustomerDashboardClient({\r\n  customerName,\r\n  _customerEmail,\r\n  userId,\r\n  initialReviewCount,\r\n  initialSubscriptionCount,\r\n  initialLikesCount,\r\n}: CustomerDashboardClientProps) {\r\n  // Animation variants for staggered animations\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      variants={containerVariants}\r\n      className=\"space-y-6\"\r\n    >\r\n      <motion.div variants={itemVariants} className=\"mb-6\">\r\n        {/* Main card container */}\r\n        <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 md:p-6 mb-4 transition-all duration-300 hover:shadow-lg\">\r\n          <div>\r\n            {/* Welcome Section */}\r\n            <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n              <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n                <User className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n                  Welcome, {customerName}\r\n                </h3>\r\n                <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n                  Manage your subscriptions and interactions\r\n                </p>\r\n              </div>\r\n              <div className=\"mt-2 sm:mt-0\">\r\n                <motion.div\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  className=\"w-auto inline-flex\"\r\n                >\r\n                  <div className=\"relative group inline-flex\">\r\n                    {/* Strong inner glow effect that fills only the button */}\r\n                    <div className=\"absolute inset-0 bg-purple-500/5 dark:bg-purple-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none\" />\r\n\r\n                    {/* Border glow effect - matches button size */}\r\n                    <div\r\n                      className=\"absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300\"\r\n                      style={{\r\n                        boxShadow: `inset 0 0 20px rgba(147, 51, 234, 0.2), 0 0 20px rgba(147, 51, 234, 0.3)`\r\n                      }}\r\n                    />\r\n\r\n                    {/* Strong decorative colored glow elements - positioned relative to button */}\r\n                    <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none\" />\r\n                    <div className=\"absolute -bottom-1 -left-1 w-4 h-4 bg-purple-500/30 rounded-full shadow-purple-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none\" />\r\n\r\n                    <Button\r\n                      asChild\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className={`\r\n                        relative overflow-hidden rounded-xl p-3\r\n                        bg-white dark:bg-black\r\n                        border border-purple-200/50 dark:border-purple-700/50\r\n                        shadow-purple-500/40 shadow-lg\r\n                        hover:shadow-xl hover:shadow-purple-500/40\r\n                        transition-all duration-300\r\n                        text-purple-500 dark:text-purple-400\r\n                        hover:bg-purple-500/5 dark:hover:bg-purple-500/10\r\n                        text-xs sm:text-sm h-auto\r\n                      `}\r\n                    >\r\n                      <Link href=\"/dashboard/customer/profile\" className=\"flex items-center relative\">\r\n                        <Settings className=\"mr-1.5 h-3.5 w-3.5\" />\r\n                        Edit Profile\r\n\r\n                        {/* Shimmer effect - only on hover */}\r\n                        <motion.div\r\n                          className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100\"\r\n                          initial={{ x: \"-100%\" }}\r\n                          whileHover={{\r\n                            x: \"100%\",\r\n                            transition: {\r\n                              duration: 0.6,\r\n                              ease: \"easeInOut\"\r\n                            }\r\n                          }}\r\n                        />\r\n                      </Link>\r\n                    </Button>\r\n                  </div>\r\n                </motion.div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Customer Metrics Overview */}\r\n            <div className=\"mb-8\">\r\n              <CustomerMetricsOverview\r\n                initialReviewCount={initialReviewCount}\r\n                initialSubscriptionCount={initialSubscriptionCount}\r\n                initialLikesCount={initialLikesCount}\r\n                userId={userId}\r\n              />\r\n            </div>\r\n\r\n\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAPA;;;;;;;AAkBe,SAAS,wBAAwB,EAC9C,YAAY,EACZ,cAAc,EACd,MAAM,EACN,kBAAkB,EAClB,wBAAwB,EACxB,iBAAiB,EACY;IAC7B,8CAA8C;IAC9C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE;IAC7D;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAQ;QACR,SAAQ;QACR,UAAU;QACV,WAAU;kBAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAAC,UAAU;YAAc,WAAU;sBAE5C,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCAEC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAA4E;gDAC9E;;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAAwD;;;;;;;;;;;;8CAIvE,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,WAAW,CAAC,wEAAwE,CAAC;oDACvF;;;;;;8DAIF,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DAEf,6LAAC,8HAAA,CAAA,SAAM;oDACL,OAAO;oDACP,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAC;;;;;;;;;;sBAUZ,CAAC;8DAED,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA8B,WAAU;;0EACjD,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAuB;0EAI3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,GAAG;gEAAQ;gEACtB,YAAY;oEACV,GAAG;oEACH,YAAY;wEACV,UAAU;wEACV,MAAM;oEACR;gEACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0LAAA,CAAA,UAAuB;gCACtB,oBAAoB;gCACpB,0BAA0B;gCAC1B,mBAAmB;gCACnB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;KA7HwB", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "file": "message-square.js", "sources": ["file://C%3A/web-app/dukancard/node_modules/lucide-react/src/icons/message-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z', key: '1lielz' }],\n];\n\n/**\n * @component @name MessageSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquare = createLucideIcon('MessageSquare', __iconNode);\n\nexport default MessageSquare;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChG,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}