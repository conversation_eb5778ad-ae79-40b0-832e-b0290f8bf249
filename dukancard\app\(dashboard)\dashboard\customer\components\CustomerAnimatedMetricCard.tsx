"use client";

import { LucideIcon } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

interface CustomerAnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "blue" | "indigo" | "purple" | "rose" | "red" | "yellow" | "brand";
  href?: string;
}

export default function CustomerAnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  href,
}: CustomerAnimatedMetricCardProps) {
  // Define modern color variants without glow effects
  const colorVariants = {
    blue: {
      iconBg: "bg-blue-50 dark:bg-blue-950/50",
      iconColor: "text-blue-600 dark:text-blue-400",
      borderColor: "border-blue-200/60 dark:border-blue-800/60",
      hoverBorder: "hover:border-blue-300 dark:hover:border-blue-700",
      buttonColor: "text-blue-600 dark:text-blue-400",
      buttonBg: "bg-blue-50 dark:bg-blue-950/50",
      buttonHover: "hover:bg-blue-100 dark:hover:bg-blue-900/50",
    },
    indigo: {
      iconBg: "bg-indigo-50 dark:bg-indigo-950/50",
      iconColor: "text-indigo-600 dark:text-indigo-400",
      borderColor: "border-indigo-200/60 dark:border-indigo-800/60",
      hoverBorder: "hover:border-indigo-300 dark:hover:border-indigo-700",
      buttonColor: "text-indigo-600 dark:text-indigo-400",
      buttonBg: "bg-indigo-50 dark:bg-indigo-950/50",
      buttonHover: "hover:bg-indigo-100 dark:hover:bg-indigo-900/50",
    },
    purple: {
      iconBg: "bg-purple-50 dark:bg-purple-950/50",
      iconColor: "text-purple-600 dark:text-purple-400",
      borderColor: "border-purple-200/60 dark:border-purple-800/60",
      hoverBorder: "hover:border-purple-300 dark:hover:border-purple-700",
      buttonColor: "text-purple-600 dark:text-purple-400",
      buttonBg: "bg-purple-50 dark:bg-purple-950/50",
      buttonHover: "hover:bg-purple-100 dark:hover:bg-purple-900/50",
    },
    rose: {
      iconBg: "bg-rose-50 dark:bg-rose-950/50",
      iconColor: "text-rose-600 dark:text-rose-400",
      borderColor: "border-rose-200/60 dark:border-rose-800/60",
      hoverBorder: "hover:border-rose-300 dark:hover:border-rose-700",
      buttonColor: "text-rose-600 dark:text-rose-400",
      buttonBg: "bg-rose-50 dark:bg-rose-950/50",
      buttonHover: "hover:bg-rose-100 dark:hover:bg-rose-900/50",
    },
    red: {
      iconBg: "bg-red-50 dark:bg-red-950/50",
      iconColor: "text-red-600 dark:text-red-400",
      borderColor: "border-red-200/60 dark:border-red-800/60",
      hoverBorder: "hover:border-red-300 dark:hover:border-red-700",
      buttonColor: "text-red-600 dark:text-red-400",
      buttonBg: "bg-red-50 dark:bg-red-950/50",
      buttonHover: "hover:bg-red-100 dark:hover:bg-red-900/50",
    },
    yellow: {
      iconBg: "bg-yellow-50 dark:bg-yellow-950/50",
      iconColor: "text-yellow-600 dark:text-yellow-400",
      borderColor: "border-yellow-200/60 dark:border-yellow-800/60",
      hoverBorder: "hover:border-yellow-300 dark:hover:border-yellow-700",
      buttonColor: "text-yellow-600 dark:text-yellow-400",
      buttonBg: "bg-yellow-50 dark:bg-yellow-950/50",
      buttonHover: "hover:bg-yellow-100 dark:hover:bg-yellow-900/50",
    },
    brand: {
      iconBg: "bg-amber-50 dark:bg-amber-950/50",
      iconColor: "text-amber-600 dark:text-amber-400",
      borderColor: "border-amber-200/60 dark:border-amber-800/60",
      hoverBorder: "hover:border-amber-300 dark:hover:border-amber-700",
      buttonColor: "text-amber-600 dark:text-amber-400",
      buttonBg: "bg-amber-50 dark:bg-amber-950/50",
      buttonHover: "hover:bg-amber-100 dark:hover:bg-amber-900/50",
    },
  };

  const selectedColor = colorVariants[color];

  const cardContent = (
    <>
      {/* Content */}
      <div className="flex flex-col items-center text-center space-y-3">
        {/* Icon */}
        <div className={`p-3 rounded-xl ${selectedColor.iconBg} transition-all duration-200 group-hover:scale-105`}>
          <Icon className={`w-6 h-6 ${selectedColor.iconColor}`} />
        </div>

        {/* Value */}
        <div className="space-y-1">
          <div className="text-2xl font-bold text-foreground">
            {value}
          </div>
          <div className="text-sm font-medium text-muted-foreground">
            {title}
          </div>
        </div>

        {/* Description */}
        <p className="text-xs text-muted-foreground">
          {description}
        </p>

        {/* Interactive Button - only show for cards with href */}
        {href && (
          <div className="mt-4">
            <Button
              asChild
              variant="outline"
              size="sm"
              className={`
                w-full text-xs font-medium transition-all duration-200
                ${selectedColor.buttonColor}
                ${selectedColor.buttonBg} ${selectedColor.buttonHover}
                border-current/20 hover:border-current/40
              `}
            >
              <Link href={href}>
                View {title}
              </Link>
            </Button>
          </div>
        )}
      </div>

      {/* Strong decorative colored glow elements */}
      <div className={`absolute -top-2 -right-2 w-10 h-10 ${selectedColor.circleColor} rounded-full ${selectedColor.circleGlow} opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md`} />
      <div className={`absolute -bottom-2 -left-2 w-8 h-8 ${selectedColor.circleColor} rounded-full ${selectedColor.circleGlow} opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md`} />
    </>
  );

  return (
    <div className={`
      group relative overflow-hidden rounded-xl p-4
      bg-white dark:bg-black
      border ${selectedColor.borderColor}
      ${selectedColor.shadowGlow} shadow-lg
      hover:shadow-xl hover:${selectedColor.shadowGlow}
      transition-all duration-300
      cursor-default
    `}>
      {cardContent}
    </div>
  );
}
