module.exports = {

"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00a78b43259bdfa35946a0918da66b9382dcd7b4dc":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "00a78b43259bdfa35946a0918da66b9382dcd7b4dc", null);
}}),
"[project]/lib/utils/addressValidation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Customer address validation utility
 * Checks if customer has complete address information
 */ __turbopack_context__.s({
    "getAddressValidationMessage": (()=>getAddressValidationMessage),
    "getMissingAddressFields": (()=>getMissingAddressFields),
    "getMissingProfileFields": (()=>getMissingProfileFields),
    "getProfileValidationMessage": (()=>getProfileValidationMessage),
    "isCustomerAddressComplete": (()=>isCustomerAddressComplete),
    "isCustomerNameComplete": (()=>isCustomerNameComplete),
    "isCustomerProfileComplete": (()=>isCustomerProfileComplete)
});
function isCustomerAddressComplete(addressData) {
    const { pincode, state, city, locality } = addressData;
    // Check if required fields are present and not empty
    return !!(pincode && pincode.trim() !== '' && state && state.trim() !== '' && city && city.trim() !== '' && locality && locality.trim() !== '');
}
function getMissingAddressFields(addressData) {
    const missing = [];
    if (!addressData.pincode || addressData.pincode.trim() === '') {
        missing.push('pincode');
    }
    if (!addressData.state || addressData.state.trim() === '') {
        missing.push('state');
    }
    if (!addressData.city || addressData.city.trim() === '') {
        missing.push('city');
    }
    if (!addressData.locality || addressData.locality.trim() === '') {
        missing.push('locality');
    }
    return missing;
}
function getAddressValidationMessage(missingFields) {
    if (missingFields.length === 0) {
        return '';
    }
    const fieldNames = missingFields.map((field)=>{
        switch(field){
            case 'pincode':
                return 'Pincode';
            case 'state':
                return 'State';
            case 'city':
                return 'City';
            case 'locality':
                return 'Locality';
            default:
                return field;
        }
    });
    if (fieldNames.length === 1) {
        return `Please update your ${fieldNames[0]} in your profile.`;
    } else if (fieldNames.length === 2) {
        return `Please update your ${fieldNames.join(' and ')} in your profile.`;
    } else {
        const lastField = fieldNames.pop();
        return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
    }
}
function isCustomerNameComplete(name) {
    return !!(name && name.trim() !== '');
}
function isCustomerProfileComplete(profileData) {
    return isCustomerNameComplete(profileData.name) && isCustomerAddressComplete(profileData);
}
function getMissingProfileFields(profileData) {
    const missing = [];
    // Check name
    if (!isCustomerNameComplete(profileData.name)) {
        missing.push('name');
    }
    // Check address fields
    const missingAddressFields = getMissingAddressFields(profileData);
    missing.push(...missingAddressFields);
    return missing;
}
function getProfileValidationMessage(missingFields) {
    if (missingFields.length === 0) {
        return '';
    }
    const fieldNames = missingFields.map((field)=>{
        switch(field){
            case 'name':
                return 'Name';
            case 'pincode':
                return 'Pincode';
            case 'state':
                return 'State';
            case 'city':
                return 'City';
            case 'locality':
                return 'Locality';
            default:
                return field;
        }
    });
    if (fieldNames.length === 1) {
        return `Please update your ${fieldNames[0]} in your profile.`;
    } else if (fieldNames.length === 2) {
        return `Please update your ${fieldNames.join(' and ')} in your profile.`;
    } else {
        const lastField = fieldNames.pop();
        return `Please update your ${fieldNames.join(', ')}, and ${lastField} in your profile.`;
    }
}
}}),
"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4044cbba3f28f10081025b33e838df6e3ddc0072ca":"validateCustomerAddress","404fd0a9be6e93cb26858695096fb8f7a80e566a2c":"validateCustomerName","407b620db7ebb4e0475b90bfef8276a8d79b4bb51a":"getCustomerAddressData","40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868":"requireCompleteAddress","40e02d24852c03895746a18f4b2a7e50cb5b140aa4":"requireCompleteName","60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab":"requireCompleteProfile"},"",""] */ __turbopack_context__.s({
    "getCustomerAddressData": (()=>getCustomerAddressData),
    "requireCompleteAddress": (()=>requireCompleteAddress),
    "requireCompleteName": (()=>requireCompleteName),
    "requireCompleteProfile": (()=>requireCompleteProfile),
    "validateCustomerAddress": (()=>validateCustomerAddress),
    "validateCustomerName": (()=>validateCustomerName)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function validateCustomerAddress(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Fetch customer address data
        const { data: profile, error } = await supabase.from('customer_profiles').select('pincode, state, city, locality, address').eq('id', userId).single();
        if (error) {
            console.error('Error fetching customer profile for address validation:', error);
            // If we can't fetch the profile, assume invalid and redirect
            return {
                isValid: false,
                message: 'Unable to verify your address information. Please update your profile.',
                redirectUrl: '/dashboard/customer/profile?message=Please update your address information'
            };
        }
        const addressData = {
            pincode: profile?.pincode,
            state: profile?.state,
            city: profile?.city,
            locality: profile?.locality,
            address: profile?.address
        };
        const isValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isCustomerAddressComplete"])(addressData);
        if (!isValid) {
            const missingFields = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getMissingAddressFields"])(addressData);
            const message = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAddressValidationMessage"])(missingFields);
            const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;
            return {
                isValid: false,
                missingFields,
                message,
                redirectUrl
            };
        }
        return {
            isValid: true
        };
    } catch (error) {
        console.error('Unexpected error during address validation:', error);
        return {
            isValid: false,
            message: 'An error occurred while validating your address. Please update your profile.',
            redirectUrl: '/dashboard/customer/profile?message=Please update your address information'
        };
    }
}
async function requireCompleteAddress(userId) {
    const validation = await validateCustomerAddress(userId);
    if (!validation.isValid && validation.redirectUrl) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])(validation.redirectUrl);
    }
}
async function validateCustomerName(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Fetch customer name data
        const { data: profile, error } = await supabase.from('customer_profiles').select('name').eq('id', userId).single();
        if (error) {
            console.error('Error fetching customer profile for name validation:', error);
            // If we can't fetch the profile, assume invalid and redirect
            return {
                isValid: false,
                message: 'Unable to verify your profile information. Please update your profile.',
                redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'
            };
        }
        // Check if name is present and not empty
        const isValid = !!(profile?.name && profile.name.trim() !== '');
        if (!isValid) {
            const message = 'Please complete your name in your profile to access the dashboard.';
            const redirectUrl = `/dashboard/customer/profile?message=${encodeURIComponent(message)}`;
            return {
                isValid: false,
                message,
                redirectUrl
            };
        }
        return {
            isValid: true
        };
    } catch (error) {
        console.error('Unexpected error during name validation:', error);
        return {
            isValid: false,
            message: 'An error occurred while validating your profile. Please update your profile.',
            redirectUrl: '/dashboard/customer/profile?message=Please update your profile information'
        };
    }
}
async function requireCompleteName(userId) {
    const validation = await validateCustomerName(userId);
    if (!validation.isValid && validation.redirectUrl) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])(validation.redirectUrl);
    }
}
async function requireCompleteProfile(userId, exemptFromAddressValidation = false) {
    // Always check name (required for all dashboard access)
    await requireCompleteName(userId);
    // Only check address if not exempt (settings page is exempt)
    if (!exemptFromAddressValidation) {
        await requireCompleteAddress(userId);
    }
}
async function getCustomerAddressData(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data: profile, error } = await supabase.from('customer_profiles').select('pincode, state, city, locality, address').eq('id', userId).single();
        if (error) {
            console.error('Error fetching customer address data:', error);
            return {
                error: 'Failed to fetch address data'
            };
        }
        return {
            data: {
                pincode: profile?.pincode,
                state: profile?.state,
                city: profile?.city,
                locality: profile?.locality,
                address: profile?.address
            }
        };
    } catch (error) {
        console.error('Unexpected error fetching address data:', error);
        return {
            error: 'An unexpected error occurred'
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    validateCustomerAddress,
    requireCompleteAddress,
    validateCustomerName,
    requireCompleteName,
    requireCompleteProfile,
    getCustomerAddressData
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(validateCustomerAddress, "4044cbba3f28f10081025b33e838df6e3ddc0072ca", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requireCompleteAddress, "40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(validateCustomerName, "404fd0a9be6e93cb26858695096fb8f7a80e566a2c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requireCompleteName, "40e02d24852c03895746a18f4b2a7e50cb5b140aa4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(requireCompleteProfile, "60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCustomerAddressData, "407b620db7ebb4e0475b90bfef8276a8d79b4bb51a", null);
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "4044cbba3f28f10081025b33e838df6e3ddc0072ca": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateCustomerAddress"]),
    "404fd0a9be6e93cb26858695096fb8f7a80e566a2c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateCustomerName"]),
    "407b620db7ebb4e0475b90bfef8276a8d79b4bb51a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCustomerAddressData"]),
    "40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteAddress"]),
    "40e02d24852c03895746a18f4b2a7e50cb5b140aa4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteName"]),
    "60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteProfile"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00a78b43259bdfa35946a0918da66b9382dcd7b4dc"]),
    "4044cbba3f28f10081025b33e838df6e3ddc0072ca": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4044cbba3f28f10081025b33e838df6e3ddc0072ca"]),
    "404fd0a9be6e93cb26858695096fb8f7a80e566a2c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["404fd0a9be6e93cb26858695096fb8f7a80e566a2c"]),
    "407b620db7ebb4e0475b90bfef8276a8d79b4bb51a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["407b620db7ebb4e0475b90bfef8276a8d79b4bb51a"]),
    "40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40a87cee1cb9a0795a4c6990ef3ca2ba202fadb868"]),
    "40e02d24852c03895746a18f4b2a7e50cb5b140aa4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40e02d24852c03895746a18f4b2a7e50cb5b140aa4"]),
    "60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60e0ef2a1f8eb9955a0b5b5ac51d408b39d02549ab"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/customer/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx <module evaluation>", "default");
}}),
"[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx", "default");
}}),
"[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx'

Expected ',', got '{'`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
"[project]/app/(dashboard)/dashboard/customer/overview/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CustomerOverviewPage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$components$2f$CustomerDashboardClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/components/CustomerDashboardClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/customerProfiles/addressValidation.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const metadata = {
    title: "Customer Dashboard Overview - Dukancard",
    robots: "noindex, nofollow"
};
async function CustomerOverviewPage() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?message=Authentication required");
    }
    // Check if customer has complete address
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$customerProfiles$2f$addressValidation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["requireCompleteProfile"])(user.id);
    // Fetch customer profile data
    const { data: profile, error: profileError } = await supabase.from("customer_profiles").select("name, email").eq("id", user.id).single();
    // Handle profile fetch error gracefully - show dashboard but maybe with default name
    if (profileError) {
        console.error("Error fetching customer profile:", profileError?.message);
    // Don't redirect, allow dashboard access but profile might be null
    }
    const customerName = profile?.name || "Valued Customer";
    // Fetch review count
    const { count: reviewCount, error: reviewError } = await supabase.from('ratings_reviews').select('*', {
        count: 'exact',
        head: true
    }).eq('user_id', user.id);
    if (reviewError) {
        console.error("Error fetching review count:", reviewError);
    }
    // Fetch subscription count
    const { count: subscriptionCount, error: subscriptionError } = await supabase.from('subscriptions').select('*', {
        count: 'exact',
        head: true
    }).eq('user_id', user.id);
    if (subscriptionError) {
        console.error("Error fetching subscription count:", subscriptionError);
    }
    // Fetch likes count
    const { count: likesCount, error: likesError } = await supabase.from('likes').select('*', {
        count: 'exact',
        head: true
    }).eq('user_id', user.id);
    if (likesError) {
        console.error("Error fetching likes count:", likesError);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$customer$2f$components$2f$CustomerDashboardClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
        customerName: customerName,
        _customerEmail: profile?.email,
        userId: user.id,
        initialReviewCount: reviewCount || 0,
        initialSubscriptionCount: subscriptionCount || 0,
        initialLikesCount: likesCount || 0
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/customer/overview/page.tsx",
        lineNumber: 72,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/customer/overview/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/customer/overview/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_0945323a._.js.map