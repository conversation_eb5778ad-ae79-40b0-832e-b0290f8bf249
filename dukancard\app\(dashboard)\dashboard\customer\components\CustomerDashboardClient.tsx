"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { User, Settings } from "lucide-react";

import CustomerMetricsOverview from "./CustomerMetricsOverview";

interface CustomerDashboardClientProps {
  customerName: string;
  userId: string;
  initialReviewCount: number;
  initialSubscriptionCount: number;
  initialLikesCount: number;
}

export default function CustomerDashboardClient({
  customerName,
  userId,
  initialReviewCount,
  initialSubscriptionCount,
  initialLikesCount,
}: CustomerDashboardClientProps) {

  return (
    <div className="space-y-8">
      {/* Welcome Section - Full Width */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6">
        <div className="p-3 rounded-xl bg-muted">
          <User className="w-6 h-6 text-foreground" />
        </div>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-foreground">
            Welcome, {customerName}
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage your subscriptions and interactions
          </p>
        </div>
        <Button
          asChild
          variant="outline"
          size="sm"
        >
          <Link href="/dashboard/customer/profile" className="flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            Edit Profile
          </Link>
        </Button>
      </div>

      {/* Customer Metrics Overview - Full Width */}
      <CustomerMetricsOverview
        initialReviewCount={initialReviewCount}
        initialSubscriptionCount={initialSubscriptionCount}
        initialLikesCount={initialLikesCount}
        userId={userId}
      />
    </div>
  );
}
