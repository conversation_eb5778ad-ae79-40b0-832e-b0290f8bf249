{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/components/CustomerAnimatedMetricCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { LucideIcon } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface CustomerAnimatedMetricCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  icon: LucideIcon;\r\n  description: string;\r\n  color: \"blue\" | \"indigo\" | \"purple\" | \"rose\" | \"red\" | \"yellow\" | \"brand\";\r\n  href?: string;\r\n}\r\n\r\nexport default function CustomerAnimatedMetricCard({\r\n  title,\r\n  value,\r\n  icon: Icon,\r\n  description,\r\n  color,\r\n  href,\r\n}: CustomerAnimatedMetricCardProps) {\r\n  // Simple, clean design without colors or hover effects\r\n  const iconColors = {\r\n    blue: \"text-blue-600 dark:text-blue-400\",\r\n    indigo: \"text-indigo-600 dark:text-indigo-400\",\r\n    purple: \"text-purple-600 dark:text-purple-400\",\r\n    rose: \"text-rose-600 dark:text-rose-400\",\r\n    red: \"text-red-600 dark:text-red-400\",\r\n    yellow: \"text-yellow-600 dark:text-yellow-400\",\r\n    brand: \"text-amber-600 dark:text-amber-400\",\r\n  };\r\n\r\n  const iconColor = iconColors[color];\r\n\r\n  const cardContent = (\r\n    <>\r\n      {/* Content */}\r\n      <div className=\"flex flex-col items-center text-center space-y-4\">\r\n        {/* Icon */}\r\n        <div className=\"p-3 rounded-xl bg-muted\">\r\n          <Icon className={`w-6 h-6 ${iconColor}`} />\r\n        </div>\r\n\r\n        {/* Value */}\r\n        <div className=\"space-y-1\">\r\n          <div className=\"text-2xl font-bold text-foreground\">\r\n            {value}\r\n          </div>\r\n          <div className=\"text-sm font-medium text-muted-foreground\">\r\n            {title}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Description */}\r\n        <p className=\"text-xs text-muted-foreground\">\r\n          {description}\r\n        </p>\r\n\r\n        {/* Interactive Button - only show for cards with href */}\r\n        {href && (\r\n          <div className=\"mt-2\">\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs font-medium\"\r\n            >\r\n              <Link href={href}>\r\n                View {title}\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n\r\n  return (\r\n    <div className=\"rounded-xl p-6 bg-card border border-border\">\r\n      {cardContent}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAee,SAAS,2BAA2B,EACjD,KAAK,EACL,KAAK,EACL,MAAM,IAAI,EACV,WAAW,EACX,KAAK,EACL,IAAI,EAC4B;IAChC,uDAAuD;IACvD,MAAM,aAAa;QACjB,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,KAAK;QACL,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,YAAY,UAAU,CAAC,MAAM;IAEnC,MAAM,4BACJ;kBAEE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAW,CAAC,QAAQ,EAAE,WAAW;;;;;;;;;;;8BAIzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAEH,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAKL,8OAAC;oBAAE,WAAU;8BACV;;;;;;gBAIF,sBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBACL,OAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;;gCAAM;gCACV;;;;;;;;;;;;;;;;;;;;;;;;IASpB,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/components/CustomerMetricsOverview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Star, MessageSquare, Users, Heart } from \"lucide-react\";\r\nimport CustomerAnimatedMetricCard from \"./CustomerAnimatedMetricCard\";\r\n\r\ninterface CustomerMetricsOverviewProps {\r\n  initialReviewCount: number;\r\n  initialSubscriptionCount: number;\r\n  initialLikesCount: number;\r\n  userId: string;\r\n}\r\n\r\nexport default function CustomerMetricsOverview({\r\n  initialReviewCount,\r\n  initialSubscriptionCount,\r\n  initialLikesCount,\r\n  userId: _userId,\r\n}: CustomerMetricsOverviewProps) {\r\n  // Since real-time is not enabled for these tables, we'll use the initial values\r\n  const reviewCount = initialReviewCount;\r\n  const subscriptionCount = initialSubscriptionCount;\r\n  const likesCount = initialLikesCount;\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"space-y-6\"\r\n    >\r\n      {/* Activity Score - Above main metrics */}\r\n      <div className=\"grid grid-cols-1 gap-4\">\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Activity Score\"\r\n          value={reviewCount + subscriptionCount * 2 + likesCount}\r\n          icon={MessageSquare}\r\n          description=\"Your engagement level\"\r\n          color=\"brand\"\r\n        />\r\n      </div>\r\n\r\n      {/* Customer Stats Section - 3 columns: Likes, Subscriptions, Reviews */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n        {/* Likes Card */}\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Likes\"\r\n          value={likesCount}\r\n          icon={Heart}\r\n          description=\"Businesses you've liked\"\r\n          color=\"red\"\r\n          href=\"/dashboard/customer/likes\"\r\n        />\r\n\r\n        {/* Subscriptions Card */}\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Followers\"\r\n          value={subscriptionCount}\r\n          icon={Users}\r\n          description=\"Businesses you're following\"\r\n          color=\"blue\"\r\n          href=\"/dashboard/customer/subscriptions\"\r\n        />\r\n\r\n        {/* Reviews Card */}\r\n        <CustomerAnimatedMetricCard\r\n          title=\"Rating\"\r\n          value={reviewCount}\r\n          icon={Star}\r\n          description=\"Reviews you've left for businesses\"\r\n          color=\"yellow\"\r\n          href=\"/dashboard/customer/reviews\"\r\n        />\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAae,SAAS,wBAAwB,EAC9C,kBAAkB,EAClB,wBAAwB,EACxB,iBAAiB,EACjB,QAAQ,OAAO,EACc;IAC7B,gFAAgF;IAChF,MAAM,cAAc;IACpB,MAAM,oBAAoB;IAC1B,MAAM,aAAa;IAEnB,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,UAA0B;oBACzB,OAAM;oBACN,OAAO,cAAc,oBAAoB,IAAI;oBAC7C,MAAM,wNAAA,CAAA,gBAAa;oBACnB,aAAY;oBACZ,OAAM;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,UAA0B;wBACzB,OAAM;wBACN,OAAO;wBACP,MAAM,oMAAA,CAAA,QAAK;wBACX,aAAY;wBACZ,OAAM;wBACN,MAAK;;;;;;kCAIP,8OAAC,0LAAA,CAAA,UAA0B;wBACzB,OAAM;wBACN,OAAO;wBACP,MAAM,oMAAA,CAAA,QAAK;wBACX,aAAY;wBACZ,OAAM;wBACN,MAAK;;;;;;kCAIP,8OAAC,0LAAA,CAAA,UAA0B;wBACzB,OAAM;wBACN,OAAO;wBACP,MAAM,kMAAA,CAAA,OAAI;wBACV,aAAY;wBACZ,OAAM;wBACN,MAAK;;;;;;;;;;;;;;;;;;AAKf", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "file": "message-square.js", "sources": ["file://C%3A/web-app/dukancard/node_modules/lucide-react/src/icons/message-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z', key: '1lielz' }],\n];\n\n/**\n * @component @name MessageSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTVhMiAyIDAgMCAxLTIgMkg3bC00IDRWNWEyIDIgMCAwIDEgMi0yaDE0YTIgMiAwIDAgMSAyIDJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/message-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageSquare = createLucideIcon('MessageSquare', __iconNode);\n\nexport default MessageSquare;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChG,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}