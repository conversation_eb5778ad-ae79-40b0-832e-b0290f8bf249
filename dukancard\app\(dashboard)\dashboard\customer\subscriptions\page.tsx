import { createClient } from '@/utils/supabase/server';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert<PERSON>riangle, Bell } from 'lucide-react';
import SubscriptionsPageClient from './components/SubscriptionsPageClient';
import { Suspense } from 'react';
import { SubscriptionListSkeleton } from '@/app/components/shared/subscriptions';
import { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';

// Import the fetchSubscriptions function
import { fetchSubscriptions } from './actions';

export const metadata: Metadata = {
  title: "My Subscriptions - Dukancard",
  robots: "noindex, nofollow",
};

export default async function CustomerSubscriptionsPage({
  searchParams
}: {
  searchParams: Promise<{ search?: string; page?: string }>
}) {
  // Properly await searchParams to fix the error
  const { search, page: pageParam } = await searchParams;
  const supabase = await createClient();
  const page = pageParam ? parseInt(pageParam) : 1;
  const searchTerm = search || "";

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your subscriptions.');
  }

  // Check if customer has complete address
  await requireCompleteProfile(user.id);

  try {
    // Fetch subscriptions with pagination and search
    const subscriptionsResult = await fetchSubscriptions(
      user.id,
      page,
      10, // 10 items per page
      searchTerm
    );

    // Full width layout without card wrapper
    return (
      <div className="space-y-6">
        <Suspense fallback={
          <div>
            {/* Header Section */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6 mb-8">
              <div className="p-3 rounded-xl bg-muted">
                <Bell className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-foreground">
                  Subscribed Businesses
                </h1>
                <p className="text-muted-foreground mt-1">
                  Businesses you&apos;re following for updates
                </p>
              </div>
            </div>

            {/* Search skeleton */}
            <div className="mb-6">
              <Skeleton className="h-10 w-full max-w-md rounded-md" />
            </div>

            {/* Content skeleton */}
            <SubscriptionListSkeleton />
          </div>
        }>
          <SubscriptionsPageClient
            initialSubscriptions={subscriptionsResult.items}
            totalCount={subscriptionsResult.totalCount}
            currentPage={subscriptionsResult.currentPage}
            searchTerm={searchTerm}
          />
        </Suspense>
      </div>
    );
  } catch (_error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Could not load your subscriptions. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }
}
