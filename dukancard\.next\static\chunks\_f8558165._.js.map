{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/planPrioritizer.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Plan Prioritizer - Handles business plan-based post prioritization\r\n * Higher tier businesses get better visibility while maintaining fairness\r\n */\r\n\r\nexport const PLAN_PRIORITY: Record<string, number> = {\r\n  'enterprise': 5,\r\n  'pro': 4,\r\n  'growth': 3,\r\n  'basic': 2,\r\n  'free': 1\r\n};\r\n\r\nexport interface BusinessGroup {\r\n  authorId: string;\r\n  priority: number;\r\n  latestPostTime: number;\r\n  posts: UnifiedPost[];\r\n}\r\n\r\n/**\r\n * Create business priority groups based on subscription plans\r\n * Similar to how LinkedIn prioritizes premium content\r\n */\r\nexport function createBusinessPriorityGroups(businessPosts: UnifiedPost[]): BusinessGroup[] {\r\n  // Group posts by business author\r\n  const businessPostsByAuthor = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!businessPostsByAuthor.has(post.author_id)) {\r\n      businessPostsByAuthor.set(post.author_id, []);\r\n    }\r\n    businessPostsByAuthor.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business group chronologically (latest first)\r\n  businessPostsByAuthor.forEach((posts, authorId) => {\r\n    businessPostsByAuthor.set(authorId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Create priority groups\r\n  return Array.from(businessPostsByAuthor.entries())\r\n    .map(([authorId, authorPosts]) => {\r\n      const latestPost = authorPosts[0];\r\n      const priority = PLAN_PRIORITY[latestPost.business_plan || 'free'] || 1;\r\n      return {\r\n        authorId,\r\n        priority,\r\n        latestPostTime: new Date(latestPost.created_at).getTime(),\r\n        posts: authorPosts\r\n      };\r\n    })\r\n    .sort((a, b) => {\r\n      // Sort by plan priority first\r\n      if (a.priority !== b.priority) {\r\n        return b.priority - a.priority; // Higher priority first\r\n      }\r\n      // If same plan, sort by latest post timestamp\r\n      return b.latestPostTime - a.latestPostTime;\r\n    });\r\n}\r\n\r\n/**\r\n * Distribute business posts with plan-based prioritization\r\n * Uses tier-based round-robin to ensure diversity within each plan level\r\n */\r\nexport function distributePrioritizedBusinessPosts(businessGroups: BusinessGroup[]): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n\r\n  // Group businesses by plan priority\r\n  const businessesByPlan = new Map<number, BusinessGroup[]>();\r\n  businessGroups.forEach(business => {\r\n    if (!businessesByPlan.has(business.priority)) {\r\n      businessesByPlan.set(business.priority, []);\r\n    }\r\n    businessesByPlan.get(business.priority)!.push(business);\r\n  });\r\n\r\n  // Sort plan priorities (highest first)\r\n  const sortedPlanPriorities = Array.from(businessesByPlan.keys()).sort((a, b) => b - a);\r\n\r\n  // Distribute posts: round-robin within each plan tier\r\n  for (const planPriority of sortedPlanPriorities) {\r\n    const businessesInPlan = businessesByPlan.get(planPriority)!;\r\n\r\n    // Create queues for round-robin distribution\r\n    const businessPostQueues = businessesInPlan.map(business => ({\r\n      ...business,\r\n      remainingPosts: [...business.posts]\r\n    }));\r\n\r\n    // Round-robin within this plan tier until all posts are distributed\r\n    while (businessPostQueues.some(queue => queue.remainingPosts.length > 0)) {\r\n      businessPostQueues.forEach(business => {\r\n        if (business.remainingPosts.length > 0) {\r\n          const post = business.remainingPosts.shift()!;\r\n          result.push(post);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get plan display name for UI purposes\r\n */\r\nexport function getPlanDisplayName(planId: string): string {\r\n  const planNames: Record<string, string> = {\r\n    'enterprise': 'Enterprise',\r\n    'pro': 'Pro',\r\n    'growth': 'Growth',\r\n    'basic': 'Basic',\r\n    'free': 'Free'\r\n  };\r\n  \r\n  return planNames[planId] || 'Free';\r\n}\r\n\r\n/**\r\n * Check if a business has premium features based on plan\r\n */\r\nexport function hasPremiumFeatures(planId: string): boolean {\r\n  const priority = PLAN_PRIORITY[planId] || 1;\r\n  return priority >= PLAN_PRIORITY.growth; // Growth and above\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAOO,MAAM,gBAAwC;IACnD,cAAc;IACd,OAAO;IACP,UAAU;IACV,SAAS;IACT,QAAQ;AACV;AAaO,SAAS,6BAA6B,aAA4B;IACvE,iCAAiC;IACjC,MAAM,wBAAwB,IAAI;IAClC,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,sBAAsB,GAAG,CAAC,KAAK,SAAS,GAAG;YAC9C,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAC9C;QACA,sBAAsB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAClD;IAEA,uEAAuE;IACvE,sBAAsB,OAAO,CAAC,CAAC,OAAO;QACpC,sBAAsB,GAAG,CAAC,UAAU,MAAM,IAAI,CAAC,CAAC,GAAG,IACjD,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,yBAAyB;IACzB,OAAO,MAAM,IAAI,CAAC,sBAAsB,OAAO,IAC5C,GAAG,CAAC,CAAC,CAAC,UAAU,YAAY;QAC3B,MAAM,aAAa,WAAW,CAAC,EAAE;QACjC,MAAM,WAAW,aAAa,CAAC,WAAW,aAAa,IAAI,OAAO,IAAI;QACtE,OAAO;YACL;YACA;YACA,gBAAgB,IAAI,KAAK,WAAW,UAAU,EAAE,OAAO;YACvD,OAAO;QACT;IACF,GACC,IAAI,CAAC,CAAC,GAAG;QACR,8BAA8B;QAC9B,IAAI,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE;YAC7B,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ,EAAE,wBAAwB;QAC1D;QACA,8CAA8C;QAC9C,OAAO,EAAE,cAAc,GAAG,EAAE,cAAc;IAC5C;AACJ;AAMO,SAAS,mCAAmC,cAA+B;IAChF,MAAM,SAAwB,EAAE;IAEhC,oCAAoC;IACpC,MAAM,mBAAmB,IAAI;IAC7B,eAAe,OAAO,CAAC,CAAA;QACrB,IAAI,CAAC,iBAAiB,GAAG,CAAC,SAAS,QAAQ,GAAG;YAC5C,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAE,EAAE;QAC5C;QACA,iBAAiB,GAAG,CAAC,SAAS,QAAQ,EAAG,IAAI,CAAC;IAChD;IAEA,uCAAuC;IACvC,MAAM,uBAAuB,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEpF,sDAAsD;IACtD,KAAK,MAAM,gBAAgB,qBAAsB;QAC/C,MAAM,mBAAmB,iBAAiB,GAAG,CAAC;QAE9C,6CAA6C;QAC7C,MAAM,qBAAqB,iBAAiB,GAAG,CAAC,CAAA,WAAY,CAAC;gBAC3D,GAAG,QAAQ;gBACX,gBAAgB;uBAAI,SAAS,KAAK;iBAAC;YACrC,CAAC;QAED,oEAAoE;QACpE,MAAO,mBAAmB,IAAI,CAAC,CAAA,QAAS,MAAM,cAAc,CAAC,MAAM,GAAG,GAAI;YACxE,mBAAmB,OAAO,CAAC,CAAA;gBACzB,IAAI,SAAS,cAAc,CAAC,MAAM,GAAG,GAAG;oBACtC,MAAM,OAAO,SAAS,cAAc,CAAC,KAAK;oBAC1C,OAAO,IAAI,CAAC;gBACd;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,YAAoC;QACxC,cAAc;QACd,OAAO;QACP,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IAEA,OAAO,SAAS,CAAC,OAAO,IAAI;AAC9B;AAKO,SAAS,mBAAmB,MAAc;IAC/C,MAAM,WAAW,aAAa,CAAC,OAAO,IAAI;IAC1C,OAAO,YAAY,cAAc,MAAM,EAAE,mBAAmB;AAC9D", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/diversityEngine.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Diversity Engine - Ensures no consecutive posts from the same author\r\n * Inspired by Facebook/Instagram feed algorithms that maintain user engagement\r\n * through content diversity and prevent feed monotony.\r\n */\r\n\r\nexport interface DiversityOptions {\r\n  maxConsecutiveFromSameAuthor?: number;\r\n  prioritizeRecency?: boolean;\r\n}\r\n\r\n/**\r\n * Apply diversity rules to prevent consecutive posts from the same author\r\n * Uses a sliding window approach similar to major social media platforms\r\n */\r\nexport function applyDiversityRules(\r\n  posts: UnifiedPost[], \r\n  options: DiversityOptions = {}\r\n): UnifiedPost[] {\r\n  const { maxConsecutiveFromSameAuthor = 1 } = options;\r\n  \r\n  if (posts.length <= 1) return posts;\r\n\r\n  const diversifiedPosts: UnifiedPost[] = [];\r\n  const remainingPosts = [...posts];\r\n  let lastAuthorId: string | null = null;\r\n  let consecutiveCount = 0;\r\n\r\n  while (remainingPosts.length > 0) {\r\n    let selectedIndex = -1;\r\n    \r\n    // First, try to find a post from a different author\r\n    for (let i = 0; i < remainingPosts.length; i++) {\r\n      const post = remainingPosts[i];\r\n      \r\n      if (post.author_id !== lastAuthorId) {\r\n        selectedIndex = i;\r\n        break;\r\n      }\r\n    }\r\n    \r\n    // If no different author found, or we haven't exceeded consecutive limit\r\n    if (selectedIndex === -1 && consecutiveCount < maxConsecutiveFromSameAuthor) {\r\n      selectedIndex = 0; // Take the first available post\r\n    }\r\n    \r\n    // If still no selection, force diversity by taking first different author\r\n    if (selectedIndex === -1) {\r\n      for (let i = 0; i < remainingPosts.length; i++) {\r\n        if (remainingPosts[i].author_id !== lastAuthorId) {\r\n          selectedIndex = i;\r\n          break;\r\n        }\r\n      }\r\n      // If still no different author, take first available (edge case)\r\n      if (selectedIndex === -1) selectedIndex = 0;\r\n    }\r\n\r\n    const selectedPost = remainingPosts.splice(selectedIndex, 1)[0];\r\n    diversifiedPosts.push(selectedPost);\r\n    \r\n    // Update tracking variables\r\n    if (selectedPost.author_id === lastAuthorId) {\r\n      consecutiveCount++;\r\n    } else {\r\n      consecutiveCount = 1;\r\n      lastAuthorId = selectedPost.author_id;\r\n    }\r\n  }\r\n\r\n  return diversifiedPosts;\r\n}\r\n\r\n/**\r\n * Group posts by author while maintaining chronological order within groups\r\n */\r\nexport function groupPostsByAuthor(posts: UnifiedPost[]): Map<string, UnifiedPost[]> {\r\n  const grouped = new Map<string, UnifiedPost[]>();\r\n  \r\n  posts.forEach(post => {\r\n    if (!grouped.has(post.author_id)) {\r\n      grouped.set(post.author_id, []);\r\n    }\r\n    grouped.get(post.author_id)!.push(post);\r\n  });\r\n  \r\n  // Sort posts within each group chronologically (latest first)\r\n  grouped.forEach((authorPosts, authorId) => {\r\n    grouped.set(authorId, authorPosts.sort((a, b) => \r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n  \r\n  return grouped;\r\n}\r\n\r\n/**\r\n * Round-robin distribution to ensure fair representation\r\n * Similar to how Instagram distributes stories from different accounts\r\n */\r\nexport function roundRobinDistribution(groupedPosts: Map<string, UnifiedPost[]>): UnifiedPost[] {\r\n  const result: UnifiedPost[] = [];\r\n  const queues = Array.from(groupedPosts.entries()).map(([authorId, posts]) => ({\r\n    authorId,\r\n    posts: [...posts]\r\n  }));\r\n\r\n  // Continue until all queues are empty\r\n  while (queues.some(queue => queue.posts.length > 0)) {\r\n    queues.forEach(queue => {\r\n      if (queue.posts.length > 0) {\r\n        const post = queue.posts.shift()!;\r\n        result.push(post);\r\n      }\r\n    });\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAiBO,SAAS,oBACd,KAAoB,EACpB,UAA4B,CAAC,CAAC;IAE9B,MAAM,EAAE,+BAA+B,CAAC,EAAE,GAAG;IAE7C,IAAI,MAAM,MAAM,IAAI,GAAG,OAAO;IAE9B,MAAM,mBAAkC,EAAE;IAC1C,MAAM,iBAAiB;WAAI;KAAM;IACjC,IAAI,eAA8B;IAClC,IAAI,mBAAmB;IAEvB,MAAO,eAAe,MAAM,GAAG,EAAG;QAChC,IAAI,gBAAgB,CAAC;QAErB,oDAAoD;QACpD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,OAAO,cAAc,CAAC,EAAE;YAE9B,IAAI,KAAK,SAAS,KAAK,cAAc;gBACnC,gBAAgB;gBAChB;YACF;QACF;QAEA,yEAAyE;QACzE,IAAI,kBAAkB,CAAC,KAAK,mBAAmB,8BAA8B;YAC3E,gBAAgB,GAAG,gCAAgC;QACrD;QAEA,0EAA0E;QAC1E,IAAI,kBAAkB,CAAC,GAAG;YACxB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;gBAC9C,IAAI,cAAc,CAAC,EAAE,CAAC,SAAS,KAAK,cAAc;oBAChD,gBAAgB;oBAChB;gBACF;YACF;YACA,iEAAiE;YACjE,IAAI,kBAAkB,CAAC,GAAG,gBAAgB;QAC5C;QAEA,MAAM,eAAe,eAAe,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE;QAC/D,iBAAiB,IAAI,CAAC;QAEtB,4BAA4B;QAC5B,IAAI,aAAa,SAAS,KAAK,cAAc;YAC3C;QACF,OAAO;YACL,mBAAmB;YACnB,eAAe,aAAa,SAAS;QACvC;IACF;IAEA,OAAO;AACT;AAKO,SAAS,mBAAmB,KAAoB;IACrD,MAAM,UAAU,IAAI;IAEpB,MAAM,OAAO,CAAC,CAAA;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,KAAK,SAAS,GAAG;YAChC,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QAChC;QACA,QAAQ,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IACpC;IAEA,8DAA8D;IAC9D,QAAQ,OAAO,CAAC,CAAC,aAAa;QAC5B,QAAQ,GAAG,CAAC,UAAU,YAAY,IAAI,CAAC,CAAC,GAAG,IACzC,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,OAAO;AACT;AAMO,SAAS,uBAAuB,YAAwC;IAC7E,MAAM,SAAwB,EAAE;IAChC,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM,GAAK,CAAC;YAC5E;YACA,OAAO;mBAAI;aAAM;QACnB,CAAC;IAED,sCAAsC;IACtC,MAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM,GAAG,GAAI;QACnD,OAAO,OAAO,CAAC,CAAA;YACb,IAAI,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC1B,MAAM,OAAO,MAAM,KAAK,CAAC,KAAK;gBAC9B,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/optimizedHybridAlgorithm.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport { PLAN_PRIORITY } from './planPrioritizer';\r\nimport { applyDiversityRules } from './diversityEngine';\r\n\r\n/**\r\n * Optimized Hybrid Algorithm for Exact Post Count\r\n * \r\n * Works with exactly the fetched posts (e.g., 10 posts) without losing any content\r\n * Strategy:\r\n * 1. Separate customer and business posts\r\n * 2. Apply plan prioritization to business posts\r\n * 3. Merge customer + business posts by timestamp\r\n * 4. Apply diversity rules\r\n * 5. Return all posts (no loss)\r\n */\r\n\r\nexport interface OptimizedHybridOptions {\r\n  enableDiversity?: boolean;\r\n  maintainChronologicalFlow?: boolean;\r\n}\r\n\r\n/**\r\n * Main optimized hybrid algorithm - processes exactly the fetched posts\r\n */\r\nexport function processOptimizedHybrid(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate customer and business posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  // Process customer posts (maintain chronological order)\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n\r\n  // Process business posts (apply plan prioritization)\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Merge both types\r\n  const mergedPosts = mergeOptimizedPosts(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(mergedPosts) : mergedPosts;\r\n}\r\n\r\n/**\r\n * Process customer posts - simple chronological sort\r\n */\r\nfunction processCustomerPostsOptimized(customerPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return [];\r\n\r\n  // Sort chronologically (latest first)\r\n  return customerPosts.sort((a, b) => \r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n}\r\n\r\n/**\r\n * Process business posts - apply plan prioritization ONLY to latest post per business\r\n * Other posts from same business compete purely on timestamp\r\n */\r\nfunction processBusinessPostsOptimized(businessPosts: UnifiedPost[]): UnifiedPost[] {\r\n  if (businessPosts.length === 0) return [];\r\n\r\n  // Group posts by business (author_id)\r\n  const postsByBusiness = new Map<string, UnifiedPost[]>();\r\n  businessPosts.forEach(post => {\r\n    if (!postsByBusiness.has(post.author_id)) {\r\n      postsByBusiness.set(post.author_id, []);\r\n    }\r\n    postsByBusiness.get(post.author_id)!.push(post);\r\n  });\r\n\r\n  // Sort posts within each business by timestamp (latest first)\r\n  postsByBusiness.forEach((posts, businessId) => {\r\n    postsByBusiness.set(businessId, posts.sort((a, b) =>\r\n      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n    ));\r\n  });\r\n\r\n  // Separate latest posts (get plan priority) from other posts (time-based only)\r\n  const latestPostsPerBusiness: UnifiedPost[] = [];\r\n  const otherPostsFromBusinesses: UnifiedPost[] = [];\r\n\r\n  postsByBusiness.forEach((posts, _businessId) => {\r\n    if (posts.length > 0) {\r\n      // First post is latest (already sorted)\r\n      latestPostsPerBusiness.push(posts[0]);\r\n\r\n      // Rest are other posts from same business\r\n      if (posts.length > 1) {\r\n        otherPostsFromBusinesses.push(...posts.slice(1));\r\n      }\r\n    }\r\n  });\r\n\r\n  // Sort latest posts by plan priority + timestamp\r\n  const prioritizedLatestPosts = latestPostsPerBusiness.sort((a, b) => {\r\n    const planA = a.business_plan || 'free';\r\n    const planB = b.business_plan || 'free';\r\n\r\n    const priorityA = PLAN_PRIORITY[planA] || 1;\r\n    const priorityB = PLAN_PRIORITY[planB] || 1;\r\n\r\n    // Sort by plan priority first\r\n    if (priorityA !== priorityB) {\r\n      return priorityB - priorityA; // Higher priority first\r\n    }\r\n\r\n    // If same plan, sort by timestamp (latest first)\r\n    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();\r\n  });\r\n\r\n  // Sort other posts purely by timestamp (no plan priority)\r\n  const timeBasedOtherPosts = otherPostsFromBusinesses.sort((a, b) =>\r\n    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\r\n  );\r\n\r\n  // Return prioritized latest posts first, then time-based other posts\r\n  return [...prioritizedLatestPosts, ...timeBasedOtherPosts];\r\n}\r\n\r\n/**\r\n * Merge customer and business posts with equal treatment\r\n * No priority between customer vs business - only plan priority within business posts\r\n */\r\nfunction mergeOptimizedPosts(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  maintainChronologicalFlow: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  if (maintainChronologicalFlow) {\r\n    // Merge all posts by timestamp - equal treatment\r\n    return [...customerPosts, ...businessPosts]\r\n      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\r\n  } else {\r\n    // Business posts first (due to plan prioritization), then customer posts\r\n    return [...businessPosts, ...customerPosts];\r\n  }\r\n}\r\n\r\n/**\r\n * Alternative approach: Intelligent interleaving\r\n * Ensures both customer and business posts get representation\r\n */\r\nexport function processOptimizedHybridWithInterleaving(\r\n  posts: UnifiedPost[],\r\n  options: OptimizedHybridOptions = {}\r\n): UnifiedPost[] {\r\n  const {\r\n    enableDiversity = true,\r\n    maintainChronologicalFlow = true\r\n  } = options;\r\n\r\n  if (posts.length === 0) return [];\r\n\r\n  // Separate and process posts\r\n  const customerPosts = posts.filter(post => post.post_source === 'customer');\r\n  const businessPosts = posts.filter(post => post.post_source === 'business');\r\n\r\n  const processedCustomerPosts = processCustomerPostsOptimized(customerPosts);\r\n  const processedBusinessPosts = processBusinessPostsOptimized(businessPosts);\r\n\r\n  // Intelligent interleaving\r\n  const interleavedPosts = intelligentInterleave(\r\n    processedCustomerPosts,\r\n    processedBusinessPosts,\r\n    maintainChronologicalFlow\r\n  );\r\n\r\n  // Apply diversity rules if enabled\r\n  return enableDiversity ? applyDiversityRules(interleavedPosts) : interleavedPosts;\r\n}\r\n\r\n/**\r\n * Intelligent interleaving of customer and business posts\r\n */\r\nfunction intelligentInterleave(\r\n  customerPosts: UnifiedPost[],\r\n  businessPosts: UnifiedPost[],\r\n  respectTimestamp: boolean\r\n): UnifiedPost[] {\r\n  if (customerPosts.length === 0) return businessPosts;\r\n  if (businessPosts.length === 0) return customerPosts;\r\n\r\n  const result: UnifiedPost[] = [];\r\n  let customerIndex = 0;\r\n  let businessIndex = 0;\r\n\r\n  // Interleave posts while respecting timestamps if enabled\r\n  while (customerIndex < customerPosts.length || businessIndex < businessPosts.length) {\r\n    const customerPost = customerPosts[customerIndex];\r\n    const businessPost = businessPosts[businessIndex];\r\n\r\n    if (!customerPost && businessPost) {\r\n      // Only business posts left\r\n      result.push(businessPost);\r\n      businessIndex++;\r\n    } else if (customerPost && !businessPost) {\r\n      // Only customer posts left\r\n      result.push(customerPost);\r\n      customerIndex++;\r\n    } else if (customerPost && businessPost) {\r\n      // Both available - decide based on timestamp or alternating pattern\r\n      if (respectTimestamp) {\r\n        const customerTime = new Date(customerPost.created_at).getTime();\r\n        const businessTime = new Date(businessPost.created_at).getTime();\r\n        \r\n        if (businessTime >= customerTime) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      } else {\r\n        // Alternating pattern - business posts get slight preference due to plan prioritization\r\n        if (result.length % 2 === 0) {\r\n          result.push(businessPost);\r\n          businessIndex++;\r\n        } else {\r\n          result.push(customerPost);\r\n          customerIndex++;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Get statistics for the optimized algorithm\r\n */\r\nexport function getOptimizedAlgorithmStats(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  originalCount: number;\r\n  processedCount: number;\r\n  customerPosts: number;\r\n  businessPosts: number;\r\n  planDistribution: Record<string, number>;\r\n  postsLost: number;\r\n  efficiency: number;\r\n} {\r\n  const planDistribution: Record<string, number> = {};\r\n  \r\n  const businessPosts = processedPosts.filter(p => p.post_source === 'business');\r\n  const customerPosts = processedPosts.filter(p => p.post_source === 'customer');\r\n  \r\n  businessPosts.forEach(post => {\r\n    const plan = post.business_plan || 'free';\r\n    planDistribution[plan] = (planDistribution[plan] || 0) + 1;\r\n  });\r\n\r\n  const postsLost = originalPosts.length - processedPosts.length;\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    originalCount: originalPosts.length,\r\n    processedCount: processedPosts.length,\r\n    customerPosts: customerPosts.length,\r\n    businessPosts: businessPosts.length,\r\n    planDistribution,\r\n    postsLost,\r\n    efficiency\r\n  };\r\n}\r\n\r\n/**\r\n * Validate that no posts are lost (should always be 100% with optimized algorithm)\r\n */\r\nexport function validateOptimizedAlgorithm(\r\n  originalPosts: UnifiedPost[],\r\n  processedPosts: UnifiedPost[]\r\n): {\r\n  isValid: boolean;\r\n  issues: string[];\r\n  efficiency: number;\r\n} {\r\n  const issues: string[] = [];\r\n  \r\n  if (originalPosts.length !== processedPosts.length) {\r\n    issues.push(`Post count mismatch: ${originalPosts.length} → ${processedPosts.length}`);\r\n  }\r\n\r\n  const originalIds = new Set(originalPosts.map(p => p.id));\r\n  const processedIds = new Set(processedPosts.map(p => p.id));\r\n  \r\n  const lostPosts: string[] = [];\r\n  originalIds.forEach(id => {\r\n    if (!processedIds.has(id)) {\r\n      lostPosts.push(id);\r\n    }\r\n  });\r\n\r\n  if (lostPosts.length > 0) {\r\n    issues.push(`Lost posts: ${lostPosts.join(', ')}`);\r\n  }\r\n\r\n  const efficiency = processedPosts.length / originalPosts.length;\r\n\r\n  return {\r\n    isValid: issues.length === 0,\r\n    issues,\r\n    efficiency\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AACA;AACA;;;AAsBO,SAAS,uBACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,uCAAuC;IACvC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,wDAAwD;IACxD,MAAM,yBAAyB,8BAA8B;IAE7D,qDAAqD;IACrD,MAAM,yBAAyB,8BAA8B;IAE7D,mBAAmB;IACnB,MAAM,cAAc,oBAClB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;AAC9D;AAEA;;CAEC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAC5B,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;AAErE;AAEA;;;CAGC,GACD,SAAS,8BAA8B,aAA4B;IACjE,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO,EAAE;IAEzC,sCAAsC;IACtC,MAAM,kBAAkB,IAAI;IAC5B,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,SAAS,GAAG;YACxC,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAE,EAAE;QACxC;QACA,gBAAgB,GAAG,CAAC,KAAK,SAAS,EAAG,IAAI,CAAC;IAC5C;IAEA,8DAA8D;IAC9D,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,gBAAgB,GAAG,CAAC,YAAY,MAAM,IAAI,CAAC,CAAC,GAAG,IAC7C,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAErE;IAEA,+EAA+E;IAC/E,MAAM,yBAAwC,EAAE;IAChD,MAAM,2BAA0C,EAAE;IAElD,gBAAgB,OAAO,CAAC,CAAC,OAAO;QAC9B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,wCAAwC;YACxC,uBAAuB,IAAI,CAAC,KAAK,CAAC,EAAE;YAEpC,0CAA0C;YAC1C,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,yBAAyB,IAAI,IAAI,MAAM,KAAK,CAAC;YAC/C;QACF;IACF;IAEA,iDAAiD;IACjD,MAAM,yBAAyB,uBAAuB,IAAI,CAAC,CAAC,GAAG;QAC7D,MAAM,QAAQ,EAAE,aAAa,IAAI;QACjC,MAAM,QAAQ,EAAE,aAAa,IAAI;QAEjC,MAAM,YAAY,0IAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAC1C,MAAM,YAAY,0IAAA,CAAA,gBAAa,CAAC,MAAM,IAAI;QAE1C,8BAA8B;QAC9B,IAAI,cAAc,WAAW;YAC3B,OAAO,YAAY,WAAW,wBAAwB;QACxD;QAEA,iDAAiD;QACjD,OAAO,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAC1E;IAEA,0DAA0D;IAC1D,MAAM,sBAAsB,yBAAyB,IAAI,CAAC,CAAC,GAAG,IAC5D,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IAGnE,qEAAqE;IACrE,OAAO;WAAI;WAA2B;KAAoB;AAC5D;AAEA;;;CAGC,GACD,SAAS,oBACP,aAA4B,EAC5B,aAA4B,EAC5B,yBAAkC;IAElC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,IAAI,2BAA2B;QAC7B,iDAAiD;QACjD,OAAO;eAAI;eAAkB;SAAc,CACxC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;IACrF,OAAO;QACL,yEAAyE;QACzE,OAAO;eAAI;eAAkB;SAAc;IAC7C;AACF;AAMO,SAAS,uCACd,KAAoB,EACpB,UAAkC,CAAC,CAAC;IAEpC,MAAM,EACJ,kBAAkB,IAAI,EACtB,4BAA4B,IAAI,EACjC,GAAG;IAEJ,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE;IAEjC,6BAA6B;IAC7B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAChE,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,WAAW,KAAK;IAEhE,MAAM,yBAAyB,8BAA8B;IAC7D,MAAM,yBAAyB,8BAA8B;IAE7D,2BAA2B;IAC3B,MAAM,mBAAmB,sBACvB,wBACA,wBACA;IAGF,mCAAmC;IACnC,OAAO,kBAAkB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD,EAAE,oBAAoB;AACnE;AAEA;;CAEC,GACD,SAAS,sBACP,aAA4B,EAC5B,aAA4B,EAC5B,gBAAyB;IAEzB,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IACvC,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,MAAM,SAAwB,EAAE;IAChC,IAAI,gBAAgB;IACpB,IAAI,gBAAgB;IAEpB,0DAA0D;IAC1D,MAAO,gBAAgB,cAAc,MAAM,IAAI,gBAAgB,cAAc,MAAM,CAAE;QACnF,MAAM,eAAe,aAAa,CAAC,cAAc;QACjD,MAAM,eAAe,aAAa,CAAC,cAAc;QAEjD,IAAI,CAAC,gBAAgB,cAAc;YACjC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,CAAC,cAAc;YACxC,2BAA2B;YAC3B,OAAO,IAAI,CAAC;YACZ;QACF,OAAO,IAAI,gBAAgB,cAAc;YACvC,oEAAoE;YACpE,IAAI,kBAAkB;gBACpB,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAC9D,MAAM,eAAe,IAAI,KAAK,aAAa,UAAU,EAAE,OAAO;gBAE9D,IAAI,gBAAgB,cAAc;oBAChC,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF,OAAO;gBACL,wFAAwF;gBACxF,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;oBAC3B,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,OAAO,IAAI,CAAC;oBACZ;gBACF;YACF;QACF;IACF;IAEA,OAAO;AACT;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAU7B,MAAM,mBAA2C,CAAC;IAElD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IACnE,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;IAEnE,cAAc,OAAO,CAAC,CAAA;QACpB,MAAM,OAAO,KAAK,aAAa,IAAI;QACnC,gBAAgB,CAAC,KAAK,GAAG,CAAC,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI;IAC3D;IAEA,MAAM,YAAY,cAAc,MAAM,GAAG,eAAe,MAAM;IAC9D,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,eAAe,cAAc,MAAM;QACnC,gBAAgB,eAAe,MAAM;QACrC,eAAe,cAAc,MAAM;QACnC,eAAe,cAAc,MAAM;QACnC;QACA;QACA;IACF;AACF;AAKO,SAAS,2BACd,aAA4B,EAC5B,cAA6B;IAM7B,MAAM,SAAmB,EAAE;IAE3B,IAAI,cAAc,MAAM,KAAK,eAAe,MAAM,EAAE;QAClD,OAAO,IAAI,CAAC,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC,GAAG,EAAE,eAAe,MAAM,EAAE;IACvF;IAEA,MAAM,cAAc,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IACvD,MAAM,eAAe,IAAI,IAAI,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAEzD,MAAM,YAAsB,EAAE;IAC9B,YAAY,OAAO,CAAC,CAAA;QAClB,IAAI,CAAC,aAAa,GAAG,CAAC,KAAK;YACzB,UAAU,IAAI,CAAC;QACjB;IACF;IAEA,IAAI,UAAU,MAAM,GAAG,GAAG;QACxB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,OAAO;IACnD;IAEA,MAAM,aAAa,eAAe,MAAM,GAAG,cAAc,MAAM;IAE/D,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/feed/postCreationHandler.ts"], "sourcesContent": ["import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\n\r\n/**\r\n * Post Creation Handler - Manages immediate post visibility after creation\r\n * \r\n * Behavior:\r\n * 1. When user creates a post -> Show at top immediately (instant feedback)\r\n * 2. When user refreshes -> Apply normal algorithm (proper positioning)\r\n * \r\n * This provides excellent UX while maintaining algorithmic integrity\r\n */\r\n\r\nexport interface PostCreationState {\r\n  justCreatedPostId?: string;\r\n  sessionId?: string;\r\n  createdAt?: string;\r\n}\r\n\r\nexport interface FeedWithCreationState {\r\n  posts: UnifiedPost[];\r\n  hasJustCreatedPost: boolean;\r\n  justCreatedPost?: UnifiedPost;\r\n}\r\n\r\n/**\r\n * Handle feed display when user just created a post\r\n * Shows new post at top for immediate feedback\r\n */\r\nexport function handlePostCreationFeed(\r\n  algorithmicPosts: UnifiedPost[],\r\n  creationState: PostCreationState\r\n): FeedWithCreationState {\r\n  \r\n  if (!creationState.justCreatedPostId) {\r\n    // No recent post creation, return normal algorithmic feed\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Find the just-created post in the algorithmic results\r\n  const justCreatedPost = algorithmicPosts.find(\r\n    post => post.id === creationState.justCreatedPostId\r\n  );\r\n\r\n  if (!justCreatedPost) {\r\n    // Post not found in current page, return normal feed\r\n    // (Post might be on a different page due to algorithm)\r\n    return {\r\n      posts: algorithmicPosts,\r\n      hasJustCreatedPost: false\r\n    };\r\n  }\r\n\r\n  // Remove the post from its algorithmic position\r\n  const otherPosts = algorithmicPosts.filter(\r\n    post => post.id !== creationState.justCreatedPostId\r\n  );\r\n\r\n  // Show just-created post at the top\r\n  return {\r\n    posts: [justCreatedPost, ...otherPosts],\r\n    hasJustCreatedPost: true,\r\n    justCreatedPost\r\n  };\r\n}\r\n\r\n/**\r\n * Create post creation state after successful post creation\r\n */\r\nexport function createPostCreationState(\r\n  postId: string,\r\n  sessionId?: string\r\n): PostCreationState {\r\n  return {\r\n    justCreatedPostId: postId,\r\n    sessionId: sessionId || generateSessionId(),\r\n    createdAt: new Date().toISOString()\r\n  };\r\n}\r\n\r\n/**\r\n * Check if post creation state is still valid (within session)\r\n */\r\nexport function isPostCreationStateValid(\r\n  creationState: PostCreationState,\r\n  currentSessionId?: string\r\n): boolean {\r\n  if (!creationState.justCreatedPostId) return false;\r\n  \r\n  // Check if it's the same session\r\n  if (creationState.sessionId && currentSessionId) {\r\n    return creationState.sessionId === currentSessionId;\r\n  }\r\n\r\n  // Check if creation was recent (within last 5 minutes as fallback)\r\n  if (creationState.createdAt) {\r\n    const createdTime = new Date(creationState.createdAt).getTime();\r\n    const now = new Date().getTime();\r\n    const fiveMinutes = 5 * 60 * 1000;\r\n    \r\n    return (now - createdTime) < fiveMinutes;\r\n  }\r\n\r\n  return false;\r\n}\r\n\r\n/**\r\n * Clear post creation state (call on refresh or navigation)\r\n */\r\nexport function clearPostCreationState(): PostCreationState {\r\n  return {};\r\n}\r\n\r\n/**\r\n * Generate a simple session ID for tracking\r\n */\r\nfunction generateSessionId(): string {\r\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n}\r\n\r\n/**\r\n * Enhanced feed response that includes creation state information\r\n */\r\nexport interface EnhancedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    hasJustCreatedPost: boolean;\r\n    justCreatedPost?: UnifiedPost;\r\n    creationState?: PostCreationState;\r\n  };\r\n}\r\n\r\n/**\r\n * Process feed with post creation handling\r\n */\r\nexport function processFeedWithCreationHandling(\r\n  algorithmicPosts: UnifiedPost[],\r\n  totalCount: number,\r\n  hasMore: boolean,\r\n  creationState?: PostCreationState\r\n): EnhancedFeedResponse {\r\n\r\n  if (!creationState || !creationState.justCreatedPostId) {\r\n    // No post creation state, return normal feed\r\n    return {\r\n      success: true,\r\n      message: 'Posts fetched successfully',\r\n      data: {\r\n        items: algorithmicPosts,\r\n        totalCount,\r\n        hasMore,\r\n        hasJustCreatedPost: false\r\n      }\r\n    };\r\n  }\r\n\r\n  // Handle post creation display\r\n  const feedWithCreation = handlePostCreationFeed(algorithmicPosts, creationState);\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Posts fetched successfully',\r\n    data: {\r\n      items: feedWithCreation.posts,\r\n      totalCount,\r\n      hasMore,\r\n      hasJustCreatedPost: feedWithCreation.hasJustCreatedPost,\r\n      justCreatedPost: feedWithCreation.justCreatedPost,\r\n      creationState\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Client-side helper to manage post creation state in localStorage/sessionStorage\r\n */\r\nexport const PostCreationStateManager = {\r\n  \r\n  /**\r\n   * Save post creation state to session storage\r\n   */\r\n  save(state: PostCreationState): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.setItem('post_creation_state', JSON.stringify(state));\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Load post creation state from session storage\r\n   */\r\n  load(): PostCreationState {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = sessionStorage.getItem('post_creation_state');\r\n      if (stored) {\r\n        try {\r\n          return JSON.parse(stored);\r\n        } catch (e) {\r\n          console.warn('Failed to parse post creation state:', e);\r\n        }\r\n      }\r\n    }\r\n    return {};\r\n  },\r\n\r\n  /**\r\n   * Clear post creation state from session storage\r\n   */\r\n  clear(): void {\r\n    if (typeof window !== 'undefined') {\r\n      sessionStorage.removeItem('post_creation_state');\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Check if current state is valid and clear if not\r\n   */\r\n  validateAndClean(): PostCreationState {\r\n    const state = this.load();\r\n    const currentSessionId = this.getCurrentSessionId();\r\n    \r\n    if (!isPostCreationStateValid(state, currentSessionId)) {\r\n      this.clear();\r\n      return {};\r\n    }\r\n    \r\n    return state;\r\n  },\r\n\r\n  /**\r\n   * Get or create current session ID\r\n   */\r\n  getCurrentSessionId(): string {\r\n    if (typeof window !== 'undefined') {\r\n      let sessionId = sessionStorage.getItem('current_session_id');\r\n      if (!sessionId) {\r\n        sessionId = generateSessionId();\r\n        sessionStorage.setItem('current_session_id', sessionId);\r\n      }\r\n      return sessionId;\r\n    }\r\n    return generateSessionId();\r\n  }\r\n};\r\n\r\n/**\r\n * Hook-like function for React components to manage post creation state\r\n */\r\nexport function usePostCreationState() {\r\n  const load = () => PostCreationStateManager.validateAndClean();\r\n  const save = (state: PostCreationState) => PostCreationStateManager.save(state);\r\n  const clear = () => PostCreationStateManager.clear();\r\n  \r\n  return { load, save, clear };\r\n}\r\n\r\n/**\r\n * Utility to mark a post as just created (call after successful post creation)\r\n */\r\nexport function markPostAsJustCreated(postId: string): void {\r\n  const state = createPostCreationState(\r\n    postId, \r\n    PostCreationStateManager.getCurrentSessionId()\r\n  );\r\n  PostCreationStateManager.save(state);\r\n}\r\n\r\n/**\r\n * Utility to check if we should show the \"just posted\" indicator\r\n */\r\nexport function shouldShowJustPostedIndicator(\r\n  post: UnifiedPost, \r\n  creationState?: PostCreationState\r\n): boolean {\r\n  if (!creationState || !creationState.justCreatedPostId) return false;\r\n  return post.id === creationState.justCreatedPostId;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AA4BO,SAAS,uBACd,gBAA+B,EAC/B,aAAgC;IAGhC,IAAI,CAAC,cAAc,iBAAiB,EAAE;QACpC,0DAA0D;QAC1D,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,wDAAwD;IACxD,MAAM,kBAAkB,iBAAiB,IAAI,CAC3C,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,IAAI,CAAC,iBAAiB;QACpB,qDAAqD;QACrD,uDAAuD;QACvD,OAAO;YACL,OAAO;YACP,oBAAoB;QACtB;IACF;IAEA,gDAAgD;IAChD,MAAM,aAAa,iBAAiB,MAAM,CACxC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,iBAAiB;IAGrD,oCAAoC;IACpC,OAAO;QACL,OAAO;YAAC;eAAoB;SAAW;QACvC,oBAAoB;QACpB;IACF;AACF;AAKO,SAAS,wBACd,MAAc,EACd,SAAkB;IAElB,OAAO;QACL,mBAAmB;QACnB,WAAW,aAAa;QACxB,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAKO,SAAS,yBACd,aAAgC,EAChC,gBAAyB;IAEzB,IAAI,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAE7C,iCAAiC;IACjC,IAAI,cAAc,SAAS,IAAI,kBAAkB;QAC/C,OAAO,cAAc,SAAS,KAAK;IACrC;IAEA,mEAAmE;IACnE,IAAI,cAAc,SAAS,EAAE;QAC3B,MAAM,cAAc,IAAI,KAAK,cAAc,SAAS,EAAE,OAAO;QAC7D,MAAM,MAAM,IAAI,OAAO,OAAO;QAC9B,MAAM,cAAc,IAAI,KAAK;QAE7B,OAAO,AAAC,MAAM,cAAe;IAC/B;IAEA,OAAO;AACT;AAKO,SAAS;IACd,OAAO,CAAC;AACV;AAEA;;CAEC,GACD,SAAS;IACP,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAsBO,SAAS,gCACd,gBAA+B,EAC/B,UAAkB,EAClB,OAAgB,EAChB,aAAiC;IAGjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE;QACtD,6CAA6C;QAC7C,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP;gBACA;gBACA,oBAAoB;YACtB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,uBAAuB,kBAAkB;IAElE,OAAO;QACL,SAAS;QACT,SAAS;QACT,MAAM;YACJ,OAAO,iBAAiB,KAAK;YAC7B;YACA;YACA,oBAAoB,iBAAiB,kBAAkB;YACvD,iBAAiB,iBAAiB,eAAe;YACjD;QACF;IACF;AACF;AAKO,MAAM,2BAA2B;IAEtC;;GAEC,GACD,MAAK,KAAwB;QAC3B,wCAAmC;YACjC,eAAe,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,MAAM,SAAS,eAAe,OAAO,CAAC;YACtC,IAAI,QAAQ;gBACV,IAAI;oBACF,OAAO,KAAK,KAAK,CAAC;gBACpB,EAAE,OAAO,GAAG;oBACV,QAAQ,IAAI,CAAC,wCAAwC;gBACvD;YACF;QACF;QACA,OAAO,CAAC;IACV;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,eAAe,UAAU,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD;QACE,MAAM,QAAQ,IAAI,CAAC,IAAI;QACvB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QAEjD,IAAI,CAAC,yBAAyB,OAAO,mBAAmB;YACtD,IAAI,CAAC,KAAK;YACV,OAAO,CAAC;QACV;QAEA,OAAO;IACT;IAEA;;GAEC,GACD;QACE,wCAAmC;YACjC,IAAI,YAAY,eAAe,OAAO,CAAC;YACvC,IAAI,CAAC,WAAW;gBACd,YAAY;gBACZ,eAAe,OAAO,CAAC,sBAAsB;YAC/C;YACA,OAAO;QACT;;IAEF;AACF;AAKO,SAAS;IACd,MAAM,OAAO,IAAM,yBAAyB,gBAAgB;IAC5D,MAAM,OAAO,CAAC,QAA6B,yBAAyB,IAAI,CAAC;IACzE,MAAM,QAAQ,IAAM,yBAAyB,KAAK;IAElD,OAAO;QAAE;QAAM;QAAM;IAAM;AAC7B;AAKO,SAAS,sBAAsB,MAAc;IAClD,MAAM,QAAQ,wBACZ,QACA,yBAAyB,mBAAmB;IAE9C,yBAAyB,IAAI,CAAC;AAChC;AAKO,SAAS,8BACd,IAAiB,EACjB,aAAiC;IAEjC,IAAI,CAAC,iBAAiB,CAAC,cAAc,iBAAiB,EAAE,OAAO;IAC/D,OAAO,KAAK,EAAE,KAAK,cAAc,iBAAiB;AACpD", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/unifiedFeed.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\nimport { FeedQueryParams } from '@/lib/types/posts';\r\nimport { processOptimizedHybrid } from '@/lib/utils/feed/optimizedHybridAlgorithm';\r\nimport {\r\n  processFeedWithCreationHandling,\r\n  PostCreationState,\r\n  EnhancedFeedResponse\r\n} from '@/lib/utils/feed/postCreationHandler';\r\n\r\nexport interface UnifiedPost {\r\n  id: string;\r\n  post_source: 'business' | 'customer';\r\n  author_id: string;\r\n  content: string;\r\n  image_url: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  city_slug: string | null;\r\n  state_slug: string | null;\r\n  locality_slug: string | null;\r\n  pincode: string | null;\r\n  product_ids: string[];\r\n  mentioned_business_ids: string[];\r\n  author_name: string | null;\r\n  author_avatar: string | null;\r\n  business_slug: string | null; // Business slug for business posts, null for customer posts\r\n  phone: string | null; // Phone number for business posts, null for customer posts\r\n  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts\r\n  business_plan: string | null; // Plan for business posts, null for customer posts\r\n}\r\n\r\nexport interface UnifiedFeedResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: {\r\n    items: UnifiedPost[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Get unified feed posts (business + customer posts) with proper pagination\r\n * Supports post creation state for immediate feedback when user creates a post\r\n */\r\nexport async function getUnifiedFeedPosts(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  const supabase = createClient();\r\n  const {\r\n    filter = 'smart',\r\n    page = 1,\r\n    limit = 10,\r\n    city_slug,\r\n    state_slug,\r\n    locality_slug,\r\n    pincode\r\n  } = params;\r\n\r\n  try {\r\n    // Get current user for smart and subscribed filters\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n\r\n    // Build base query\r\n    let query = supabase\r\n      .from('unified_posts')\r\n      .select('*', { count: 'exact' });\r\n\r\n    // Apply filters based on filter type\r\n    switch (filter) {\r\n      case 'smart':\r\n        if (user) {\r\n          // Get user's subscribed businesses for smart feed\r\n          const { data: subscriptions } = await supabase\r\n            .from('subscriptions')\r\n            .select('business_profile_id')\r\n            .eq('user_id', user.id);\r\n\r\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\r\n\r\n          // Try to get user's location from both customer and business profiles\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase\r\n              .from('customer_profiles')\r\n              .select('city_slug, state_slug, locality_slug, pincode')\r\n              .eq('id', user.id)\r\n              .single(),\r\n            supabase\r\n              .from('business_profiles')\r\n              .select('city_slug, state_slug, locality_slug, pincode')\r\n              .eq('id', user.id)\r\n              .single()\r\n          ]);\r\n\r\n          // Use whichever profile exists\r\n          const userProfile = customerProfile.data || businessProfile.data;\r\n\r\n          // Build smart feed conditions\r\n          const conditions = [];\r\n\r\n          // Subscribed businesses\r\n          if (subscribedBusinessIds.length > 0) {\r\n            conditions.push(`and(post_source.eq.business,author_id.in.(${subscribedBusinessIds.join(',')}))`);\r\n          }\r\n\r\n          // User's own posts (check both customer and business posts)\r\n          conditions.push(`and(post_source.eq.customer,author_id.eq.${user.id})`);\r\n          conditions.push(`and(post_source.eq.business,author_id.eq.${user.id})`);\r\n\r\n          // Local posts based on user location\r\n          if (userProfile?.locality_slug) {\r\n            conditions.push(`locality_slug.eq.${userProfile.locality_slug}`);\r\n          }\r\n          if (userProfile?.pincode) {\r\n            conditions.push(`pincode.eq.${userProfile.pincode}`);\r\n          }\r\n          if (userProfile?.city_slug) {\r\n            conditions.push(`city_slug.eq.${userProfile.city_slug}`);\r\n          }\r\n\r\n          if (conditions.length > 0) {\r\n            query = query.or(conditions.join(','));\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'subscribed':\r\n        if (user) {\r\n          const { data: subscriptions } = await supabase\r\n            .from('subscriptions')\r\n            .select('business_profile_id')\r\n            .eq('user_id', user.id);\r\n\r\n          const subscribedBusinessIds = subscriptions?.map(s => s.business_profile_id) || [];\r\n\r\n          if (subscribedBusinessIds.length > 0) {\r\n            query = query\r\n              .eq('post_source', 'business')\r\n              .in('author_id', subscribedBusinessIds);\r\n          } else {\r\n            // No subscriptions, return empty result\r\n            return {\r\n              success: true,\r\n              message: 'No subscribed businesses found',\r\n              data: {\r\n                items: [],\r\n                totalCount: 0,\r\n                hasMore: false,\r\n                hasJustCreatedPost: false\r\n              }\r\n            };\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'locality':\r\n        if (locality_slug) {\r\n          query = query.eq('locality_slug', locality_slug);\r\n        } else if (user) {\r\n          // If no locality_slug provided, get user's locality from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('locality_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('locality_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userLocality = customerProfile.data?.locality_slug || businessProfile.data?.locality_slug;\r\n          if (userLocality) {\r\n            query = query.eq('locality_slug', userLocality);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'pincode':\r\n        if (pincode) {\r\n          query = query.eq('pincode', pincode);\r\n        } else if (user) {\r\n          // If no pincode provided, get user's pincode from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('pincode').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('pincode').eq('id', user.id).single()\r\n          ]);\r\n          const userPincode = customerProfile.data?.pincode || businessProfile.data?.pincode;\r\n          if (userPincode) {\r\n            query = query.eq('pincode', userPincode);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'city':\r\n        if (city_slug) {\r\n          query = query.eq('city_slug', city_slug);\r\n        } else if (user) {\r\n          // If no city_slug provided, get user's city from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('city_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('city_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userCity = customerProfile.data?.city_slug || businessProfile.data?.city_slug;\r\n          if (userCity) {\r\n            query = query.eq('city_slug', userCity);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'state':\r\n        if (state_slug) {\r\n          query = query.eq('state_slug', state_slug);\r\n        } else if (user) {\r\n          // If no state_slug provided, get user's state from their profile\r\n          const [customerProfile, businessProfile] = await Promise.all([\r\n            supabase.from('customer_profiles').select('state_slug').eq('id', user.id).single(),\r\n            supabase.from('business_profiles').select('state_slug').eq('id', user.id).single()\r\n          ]);\r\n          const userState = customerProfile.data?.state_slug || businessProfile.data?.state_slug;\r\n          if (userState) {\r\n            query = query.eq('state_slug', userState);\r\n          }\r\n        }\r\n        break;\r\n\r\n      case 'all':\r\n        // No additional filters for 'all'\r\n        break;\r\n    }\r\n\r\n    // Fetch exactly the target number of posts to prevent post loss\r\n    // Algorithm will arrange these posts optimally without losing any content\r\n    const from = (page - 1) * limit; // Standard pagination\r\n    const to = from + limit - 1;\r\n\r\n    // Execute query with chronological ordering (prioritization applied client-side)\r\n    const { data, error, count } = await query\r\n      .order('created_at', { ascending: false })\r\n      .range(from, to);\r\n\r\n    if (error) {\r\n      console.error('Error fetching unified feed posts:', error);\r\n      return {\r\n        success: false,\r\n        message: 'Failed to fetch posts',\r\n        error: error.message\r\n      };\r\n    }\r\n\r\n    // Apply optimized hybrid algorithm to ALL feed types\r\n    // Processes exactly the fetched posts without losing any content\r\n    // Business posts get plan prioritization, customer posts maintain chronological order\r\n    // Works with location filters (locality, pincode, city, state, all)\r\n    const prioritizedData = data ? processOptimizedHybrid(data, {\r\n      enableDiversity: true,\r\n      maintainChronologicalFlow: true\r\n    }) : [];\r\n\r\n    const totalCount = count || 0;\r\n    // Standard pagination logic - no posts lost\r\n    const hasMore = prioritizedData.length === limit && (from + limit) < totalCount;\r\n\r\n    // Handle post creation state for immediate feedback\r\n    return processFeedWithCreationHandling(\r\n      prioritizedData,\r\n      totalCount,\r\n      hasMore,\r\n      creationState\r\n    );\r\n\r\n  } catch (error) {\r\n    console.error('Unexpected error in getUnifiedFeedPosts:', error);\r\n    return {\r\n      success: false,\r\n      message: 'An unexpected error occurred',\r\n      error: error instanceof Error ? error.message : 'Unknown error'\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get unified feed posts with author information populated\r\n * Author information is now included directly in the unified_posts view\r\n */\r\nexport async function getUnifiedFeedPostsWithAuthors(\r\n  params: FeedQueryParams,\r\n  creationState?: PostCreationState\r\n): Promise<EnhancedFeedResponse> {\r\n  // Since author information is now included in the unified_posts view,\r\n  // we can just return the result from getUnifiedFeedPosts directly\r\n  return await getUnifiedFeedPosts(params, creationState);\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AA2CO,eAAe,oBACpB,MAAuB,EACvB,aAAiC;IAEjC,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,SAAS,OAAO,EAChB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,EACT,UAAU,EACV,aAAa,EACb,OAAO,EACR,GAAG;IAEJ,IAAI;QACF,oDAAoD;QACpD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,mBAAmB;QACnB,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ;QAEhC,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,IAAI,MAAM;oBACR,kDAAkD;oBAClD,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,sEAAsE;oBACtE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,iDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBACT,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,iDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;qBACV;oBAED,+BAA+B;oBAC/B,MAAM,cAAc,gBAAgB,IAAI,IAAI,gBAAgB,IAAI;oBAEhE,8BAA8B;oBAC9B,MAAM,aAAa,EAAE;oBAErB,wBAAwB;oBACxB,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,WAAW,IAAI,CAAC,CAAC,0CAA0C,EAAE,sBAAsB,IAAI,CAAC,KAAK,EAAE,CAAC;oBAClG;oBAEA,4DAA4D;oBAC5D,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBACtE,WAAW,IAAI,CAAC,CAAC,yCAAyC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;oBAEtE,qCAAqC;oBACrC,IAAI,aAAa,eAAe;wBAC9B,WAAW,IAAI,CAAC,CAAC,iBAAiB,EAAE,YAAY,aAAa,EAAE;oBACjE;oBACA,IAAI,aAAa,SAAS;wBACxB,WAAW,IAAI,CAAC,CAAC,WAAW,EAAE,YAAY,OAAO,EAAE;oBACrD;oBACA,IAAI,aAAa,WAAW;wBAC1B,WAAW,IAAI,CAAC,CAAC,aAAa,EAAE,YAAY,SAAS,EAAE;oBACzD;oBAEA,IAAI,WAAW,MAAM,GAAG,GAAG;wBACzB,QAAQ,MAAM,EAAE,CAAC,WAAW,IAAI,CAAC;oBACnC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,MAAM;oBACR,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,iBACL,MAAM,CAAC,uBACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,wBAAwB,eAAe,IAAI,CAAA,IAAK,EAAE,mBAAmB,KAAK,EAAE;oBAElF,IAAI,sBAAsB,MAAM,GAAG,GAAG;wBACpC,QAAQ,MACL,EAAE,CAAC,eAAe,YAClB,EAAE,CAAC,aAAa;oBACrB,OAAO;wBACL,wCAAwC;wBACxC,OAAO;4BACL,SAAS;4BACT,SAAS;4BACT,MAAM;gCACJ,OAAO,EAAE;gCACT,YAAY;gCACZ,SAAS;gCACT,oBAAoB;4BACtB;wBACF;oBACF;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,eAAe;oBACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;gBACpC,OAAO,IAAI,MAAM;oBACf,uEAAuE;oBACvE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBACnF,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBACpF;oBACD,MAAM,eAAe,gBAAgB,IAAI,EAAE,iBAAiB,gBAAgB,IAAI,EAAE;oBAClF,IAAI,cAAc;wBAChB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;oBACpC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,SAAS;oBACX,QAAQ,MAAM,EAAE,CAAC,WAAW;gBAC9B,OAAO,IAAI,MAAM;oBACf,gEAAgE;oBAChE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAC7E,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,WAAW,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBAC9E;oBACD,MAAM,cAAc,gBAAgB,IAAI,EAAE,WAAW,gBAAgB,IAAI,EAAE;oBAC3E,IAAI,aAAa;wBACf,QAAQ,MAAM,EAAE,CAAC,WAAW;oBAC9B;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW;oBACb,QAAQ,MAAM,EAAE,CAAC,aAAa;gBAChC,OAAO,IAAI,MAAM;oBACf,+DAA+D;oBAC/D,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAC/E,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBAChF;oBACD,MAAM,WAAW,gBAAgB,IAAI,EAAE,aAAa,gBAAgB,IAAI,EAAE;oBAC1E,IAAI,UAAU;wBACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;oBAChC;gBACF;gBACA;YAEF,KAAK;gBACH,IAAI,YAAY;oBACd,QAAQ,MAAM,EAAE,CAAC,cAAc;gBACjC,OAAO,IAAI,MAAM;oBACf,iEAAiE;oBACjE,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;wBAC3D,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;wBAChF,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC,cAAc,EAAE,CAAC,MAAM,KAAK,EAAE,EAAE,MAAM;qBACjF;oBACD,MAAM,YAAY,gBAAgB,IAAI,EAAE,cAAc,gBAAgB,IAAI,EAAE;oBAC5E,IAAI,WAAW;wBACb,QAAQ,MAAM,EAAE,CAAC,cAAc;oBACjC;gBACF;gBACA;YAEF,KAAK;gBAEH;QACJ;QAEA,gEAAgE;QAChE,0EAA0E;QAC1E,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,sBAAsB;QACvD,MAAM,KAAK,OAAO,QAAQ;QAE1B,iFAAiF;QACjF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,MAClC,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC,MAAM;QAEf,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,MAAM,OAAO;YACtB;QACF;QAEA,qDAAqD;QACrD,iEAAiE;QACjE,sFAAsF;QACtF,oEAAoE;QACpE,MAAM,kBAAkB,OAAO,CAAA,GAAA,mJAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;YAC1D,iBAAiB;YACjB,2BAA2B;QAC7B,KAAK,EAAE;QAEP,MAAM,aAAa,SAAS;QAC5B,4CAA4C;QAC5C,MAAM,UAAU,gBAAgB,MAAM,KAAK,SAAS,AAAC,OAAO,QAAS;QAErE,oDAAoD;QACpD,OAAO,CAAA,GAAA,8IAAA,CAAA,kCAA+B,AAAD,EACnC,iBACA,YACA,SACA;IAGJ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAMO,eAAe,+BACpB,MAAuB,EACvB,aAAiC;IAEjC,sEAAsE;IACtE,kEAAkE;IAClE,OAAO,MAAM,oBAAoB,QAAQ;AAC3C", "debugId": null}}, {"offset": {"line": 793, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/PostActions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { MessageCircle, Phone } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\ninterface PostActionsProps {\r\n  business: {\r\n    business_name: string | null;\r\n    whatsapp_number: string | null;\r\n    phone: string | null;\r\n  };\r\n  hasWhatsApp: boolean;\r\n  hasPhone: boolean;\r\n}\r\n\r\nexport default function PostActions({ business, hasWhatsApp, hasPhone }: PostActionsProps) {\r\n  // Handle contact actions\r\n  const handleWhatsAppClick = () => {\r\n    if (hasWhatsApp) {\r\n      const whatsappNumber = business.whatsapp_number?.replace(/\\D/g, ''); // Remove non-digits\r\n      const message = `Hi ${business.business_name}, I saw your post and would like to know more.`;\r\n      window.open(`https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`, '_blank');\r\n    }\r\n  };\r\n\r\n  const handlePhoneClick = () => {\r\n    if (hasPhone) {\r\n      window.open(`tel:${business.phone}`, '_self');\r\n    }\r\n  };\r\n\r\n  const buttonVariants = {\r\n    hover: { scale: 1.02, transition: { duration: 0.2 } },\r\n    tap: { scale: 0.98, transition: { duration: 0.1 } }\r\n  };\r\n\r\n  // Only show buttons if they have valid contact info - match React Native behavior\r\n  const showWhatsApp = hasWhatsApp;\r\n  const showPhone = hasPhone;\r\n\r\n  // If neither button should be shown, don't render anything\r\n  if (!showWhatsApp && !showPhone) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex gap-3\">\r\n      {showWhatsApp && (\r\n        <motion.div\r\n          variants={buttonVariants}\r\n          whileHover=\"hover\"\r\n          whileTap=\"tap\"\r\n          className=\"flex-1\"\r\n        >\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleWhatsAppClick}\r\n            className=\"w-full h-10 bg-white dark:bg-black border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 hover:bg-green-500 hover:text-white hover:border-green-500 transition-all duration-300 cursor-pointer\"\r\n          >\r\n            <MessageCircle className=\"h-4 w-4 mr-2\" />\r\n            WhatsApp\r\n          </Button>\r\n        </motion.div>\r\n      )}\r\n\r\n      {showPhone && (\r\n        <motion.div\r\n          variants={buttonVariants}\r\n          whileHover=\"hover\"\r\n          whileTap=\"tap\"\r\n          className=\"flex-1\"\r\n        >\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handlePhoneClick}\r\n            className=\"w-full h-10 bg-white dark:bg-black border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-blue-500 hover:text-white hover:border-blue-500 transition-all duration-300 cursor-pointer\"\r\n          >\r\n            <Phone className=\"h-4 w-4 mr-2\" />\r\n            Call Now\r\n          </Button>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAgBe,SAAS,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAoB;IACvF,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,aAAa;YACf,MAAM,iBAAiB,SAAS,eAAe,EAAE,QAAQ,OAAO,KAAK,oBAAoB;YACzF,MAAM,UAAU,CAAC,GAAG,EAAE,SAAS,aAAa,CAAC,8CAA8C,CAAC;YAC5F,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,UAAU,EAAE;QACrF;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,UAAU;YACZ,OAAO,IAAI,CAAC,CAAC,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO;YAAE,OAAO;YAAM,YAAY;gBAAE,UAAU;YAAI;QAAE;QACpD,KAAK;YAAE,OAAO;YAAM,YAAY;gBAAE,UAAU;YAAI;QAAE;IACpD;IAEA,kFAAkF;IAClF,MAAM,eAAe;IACrB,MAAM,YAAY;IAElB,2DAA2D;IAC3D,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAC/B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,8BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,YAAW;gBACX,UAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;YAM/C,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,YAAW;gBACX,UAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAO9C;KAvEwB", "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ProductListItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\n\r\ninterface ProductListItemProps {\r\n  product: ProductServiceData | NearbyProduct;\r\n  isLink?: boolean;\r\n}\r\n\r\n// Helper to format currency\r\nconst formatCurrency = (amount: number | null | undefined) => {\r\n  if (amount === null || amount === undefined) return null;\r\n  return amount.toLocaleString(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency: \"INR\",\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  });\r\n};\r\n\r\n// Animation variants\r\nconst cardContainerVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  show: { opacity: 1, y: 0 },\r\n};\r\n\r\nconst discountBadgeVariants = {\r\n  initial: { scale: 0.9, opacity: 0 },\r\n  animate: {\r\n    scale: 1,\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.5,\r\n      type: \"spring\",\r\n      stiffness: 400,\r\n      damping: 10,\r\n    },\r\n  },\r\n  hover: {\r\n    scale: 1.05,\r\n    rotate: -2,\r\n    transition: { type: \"spring\", stiffness: 500 },\r\n  },\r\n};\r\n\r\nexport default function ProductListItem({\r\n  product,\r\n  isLink = true,\r\n}: ProductListItemProps) {\r\n  // If isLink is true, we'll wrap the content in a link\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const formattedBasePrice = formatCurrency(product.base_price);\r\n  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price\r\n\r\n  // Determine final price and if there's a discount shown\r\n  let finalPrice = formattedBasePrice;\r\n  let priceToShowStrikethrough: string | null = null;\r\n  let discountPercentage = 0;\r\n\r\n  const hasDiscountedPrice =\r\n    typeof product.discounted_price === \"number\" &&\r\n    product.discounted_price > 0;\r\n  const hasBasePrice =\r\n    typeof product.base_price === \"number\" && product.base_price > 0;\r\n\r\n  if (\r\n    hasDiscountedPrice &&\r\n    hasBasePrice &&\r\n    product.discounted_price! < product.base_price!\r\n  ) {\r\n    // Scenario 1: Discounted price is valid and less than base price\r\n    finalPrice = formattedDiscountedPrice;\r\n    priceToShowStrikethrough = formattedBasePrice; // Strike through base price\r\n    discountPercentage = Math.round(\r\n      ((product.base_price! - product.discounted_price!) /\r\n        product.base_price!) *\r\n        100\r\n    );\r\n  } else {\r\n    // Scenario 2: No discount applicable, show base price\r\n    finalPrice = formattedBasePrice;\r\n    priceToShowStrikethrough = null;\r\n    discountPercentage = 0;\r\n  }\r\n\r\n  // Ensure finalPrice has a fallback if both prices are null/undefined\r\n  if (!finalPrice) {\r\n    finalPrice = \"Price unavailable\";\r\n  }\r\n\r\n  const showDiscountBadge = discountPercentage > 0;\r\n\r\n  // State for lazy loading images\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n\r\n  // Check if product is out of stock\r\n  const isOutOfStock = !product.is_available;\r\n\r\n  // Ensure we're not using business_id as a key\r\n  // Use the product's own ID for any keys needed\r\n  const content = (\r\n    <motion.div\r\n      variants={cardContainerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"show\"\r\n      className=\"w-full overflow-hidden\"\r\n    >\r\n      <div className=\"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg\">\r\n        <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n          {/* Image container */}\r\n          <div className=\"relative w-full overflow-hidden rounded-t-xl\">\r\n            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}\r\n            {/* Determine the image URL to use */}\r\n            {(() => {\r\n              // Get the image URL to display\r\n              let imageUrl = product.image_url;\r\n\r\n              // If product has images array and it's not empty, use the featured image\r\n              if (product.images && Array.isArray(product.images) && product.images.length > 0) {\r\n                const featuredIndex = typeof product.featured_image_index === 'number'\r\n                  ? Math.min(product.featured_image_index, product.images.length - 1)\r\n                  : 0;\r\n                imageUrl = product.images[featuredIndex];\r\n              }\r\n\r\n              if (imageUrl && !imageError) {\r\n                return (\r\n                  <div className=\"overflow-hidden\">\r\n                    {!isImageLoaded && (\r\n                      <Skeleton className=\"absolute inset-0 rounded-t-xl\" />\r\n                    )}\r\n                    <motion.div className=\"w-full\">\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={product.name ?? \"Product image\"}\r\n                        width={500}\r\n                        height={750}\r\n                        className={`w-full aspect-square object-cover ${\r\n                          isOutOfStock\r\n                            ? \"filter grayscale opacity-70 transition-all duration-500\"\r\n                            : \"\"\r\n                        } ${\r\n                          isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                        } max-w-full`}\r\n                        loading=\"lazy\"\r\n                        onError={() => setImageError(true)}\r\n                        onLoad={() => setIsImageLoaded(true)}\r\n                        quality={80}\r\n                        blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=\"\r\n                        placeholder=\"blur\"\r\n                        style={{ objectFit: \"cover\" }}\r\n                      />\r\n                    </motion.div>\r\n                  </div>\r\n                );\r\n              } else {\r\n                return (\r\n                  <div className=\"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl\">\r\n                    <svg\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={1}\r\n                        d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                );\r\n              }\r\n            })()}\r\n\r\n            {/* Out of Stock Overlay */}\r\n            {isOutOfStock && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40\">\r\n                <div className=\"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground\">\r\n                  <span className=\"font-medium tracking-wide uppercase text-xs sm:text-sm\">\r\n                    Out of Stock\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Discount Badge Overlay */}\r\n            {showDiscountBadge && (\r\n              <AnimatePresence>\r\n                <motion.div\r\n                  key={`discount-badge-${product.id}`}\r\n                  variants={discountBadgeVariants}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  whileHover=\"hover\"\r\n                  className={cn(\r\n                    \"absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg\",\r\n                    \"bg-destructive\",\r\n                    \"text-destructive-foreground border border-destructive-foreground/20\",\r\n                    \"transform-gpu\"\r\n                  )}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center\">\r\n                    <span className=\"text-[7px] sm:text-[9px] md:text-[10px] font-medium\">\r\n                      SAVE\r\n                    </span>\r\n                    <span className=\"text-[9px] sm:text-xs md:text-sm leading-none\">\r\n                      {discountPercentage}%\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n              </AnimatePresence>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1\">\r\n            {/* Title */}\r\n            <p className=\"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden\">\r\n              {product.name ?? \"Unnamed Product\"}\r\n            </p>\r\n\r\n            {/* Description (optional) */}\r\n            {product.description && (\r\n              <p className=\"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate\">\r\n                {product.description}\r\n              </p>\r\n            )}\r\n\r\n            {/* Price and Badge Container */}\r\n            <div className=\"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1\">\r\n              {/* Price Group */}\r\n              <div className=\"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full\">\r\n                {finalPrice && (\r\n                  <p className=\"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full\">\r\n                    {finalPrice}\r\n                  </p>\r\n                )}\r\n                {priceToShowStrikethrough && (\r\n                  <p className=\"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500\">\r\n                    {priceToShowStrikethrough}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Product Type Badge removed as per instructions */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  // If isLink is true, wrap the content in a link to the product detail page\r\n  if (isLink && \"business_slug\" in product && product.business_slug) {\r\n    return (\r\n      <Link\r\n        href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n        className=\"block h-full\"\r\n      >\r\n        {content}\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  // Otherwise, just return the content\r\n  return content;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;;;AARA;;;;;;;AAgBA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;IACpD,OAAO,OAAO,cAAc,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEA,qBAAqB;AACrB,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAE;AAC3B;AAEA,MAAM,wBAAwB;IAC5B,SAAS;QAAE,OAAO;QAAK,SAAS;IAAE;IAClC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,CAAC;QACT,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;IAC/C;AACF;AAEe,SAAS,gBAAgB,EACtC,OAAO,EACP,SAAS,IAAI,EACQ;;IACrB,sDAAsD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,eAAe,QAAQ,UAAU;IAC5D,MAAM,2BAA2B,eAAe,QAAQ,gBAAgB,GAAG,0BAA0B;IAErG,wDAAwD;IACxD,IAAI,aAAa;IACjB,IAAI,2BAA0C;IAC9C,IAAI,qBAAqB;IAEzB,MAAM,qBACJ,OAAO,QAAQ,gBAAgB,KAAK,YACpC,QAAQ,gBAAgB,GAAG;IAC7B,MAAM,eACJ,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;IAEjE,IACE,sBACA,gBACA,QAAQ,gBAAgB,GAAI,QAAQ,UAAU,EAC9C;QACA,iEAAiE;QACjE,aAAa;QACb,2BAA2B,oBAAoB,4BAA4B;QAC3E,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,QAAQ,UAAU,GAAI,QAAQ,gBAAgB,AAAC,IAC/C,QAAQ,UAAU,GAClB;IAEN,OAAO;QACL,sDAAsD;QACtD,aAAa;QACb,2BAA2B;QAC3B,qBAAqB;IACvB;IAEA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,aAAa;IACf;IAEA,MAAM,oBAAoB,qBAAqB;IAE/C,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,MAAM,eAAe,CAAC,QAAQ,YAAY;IAE1C,8CAA8C;IAC9C,+CAA+C;IAC/C,MAAM,wBACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BAGZ,CAAC;gCACA,+BAA+B;gCAC/B,IAAI,WAAW,QAAQ,SAAS;gCAEhC,yEAAyE;gCACzE,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oCAChF,MAAM,gBAAgB,OAAO,QAAQ,oBAAoB,KAAK,WAC1D,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,KAC/D;oCACJ,WAAW,QAAQ,MAAM,CAAC,cAAc;gCAC1C;gCAEA,IAAI,YAAY,CAAC,YAAY;oCAC3B,qBACE,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;0DACpB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,QAAQ,IAAI,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAW,CAAC,kCAAkC,EAC5C,eACI,4DACA,GACL,CAAC,EACA,gBAAgB,gBAAgB,YACjC,WAAW,CAAC;oDACb,SAAQ;oDACR,SAAS,IAAM,cAAc;oDAC7B,QAAQ,IAAM,iBAAiB;oDAC/B,SAAS;oDACT,aAAY;oDACZ,aAAY;oDACZ,OAAO;wDAAE,WAAW;oDAAQ;;;;;;;;;;;;;;;;;gCAKtC,OAAO;oCACL,qBACE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;gCAKZ;4BACF,CAAC;4BAGA,8BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;;;;;4BAQ9E,mCACC,6LAAC,4LAAA,CAAA,kBAAe;0CACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qIACA,kBACA,uEACA;8CAGF,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsD;;;;;;0DAGtE,6LAAC;gDAAK,WAAU;;oDACb;oDAAmB;;;;;;;;;;;;;mCAjBnB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;kCA0B3C,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI,IAAI;;;;;;4BAIlB,QAAQ,WAAW,kBAClB,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAKxB,6LAAC;gCAAI,WAAU;0CAEb,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,4BACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;wCAGJ,0CACC,6LAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAanB,2EAA2E;IAC3E,IAAI,UAAU,mBAAmB,WAAW,QAAQ,aAAa,EAAE;QACjE,qBACE,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;YACvE,WAAU;sBAET;;;;;;IAGP;IAEA,qCAAqC;IACrC,OAAO;AACT;GAjOwB;KAAA", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/products/fetchProductsByIds.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\n\r\nexport interface ProductData {\r\n  id: string;\r\n  name: string;\r\n  base_price: number | null;\r\n  discounted_price: number | null;\r\n  image_url: string | null;\r\n  slug: string | null;\r\n}\r\n\r\n/**\r\n * Fetch products by their IDs using admin client to bypass RLS\r\n * This is used for displaying linked products in feed posts\r\n */\r\nexport async function fetchProductsByIds(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductData[];\r\n  error?: string;\r\n}> {\r\n  if (!productIds || productIds.length === 0) {\r\n    return {\r\n      success: true,\r\n      data: []\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    const { data, error } = await supabaseAdmin\r\n      .from('products_services')\r\n      .select('id, name, base_price, discounted_price, image_url, slug')\r\n      .in('id', productIds)\r\n      .eq('is_available', true);\r\n\r\n    if (error) {\r\n      console.error('Error fetching products by IDs:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Failed to fetch products'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || []\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in fetchProductsByIds:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred'\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAiBsB,qBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1383, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/addressUtils.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\n\r\nexport interface PostAddress {\r\n  locality: string;\r\n  city: string;\r\n  state: string;\r\n  pincode: string;\r\n}\r\n\r\n/**\r\n * Fetch real address data from pincodes table using post slugs\r\n */\r\nexport async function fetchPostAddress(\r\n  locality_slug?: string | null,\r\n  city_slug?: string | null,\r\n  state_slug?: string | null,\r\n  pincode?: string | null\r\n): Promise<PostAddress | null> {\r\n  if (!pincode) {\r\n    return null;\r\n  }\r\n\r\n  const supabase = createClient();\r\n\r\n  try {\r\n    // Build query conditions\r\n    let query = supabase\r\n      .from('pincodes')\r\n      .select('OfficeName, DivisionName, StateName, Pincode')\r\n      .eq('Pincode', pincode);\r\n\r\n    // Add additional filters if available\r\n    if (city_slug) {\r\n      query = query.eq('city_slug', city_slug);\r\n    }\r\n    if (state_slug) {\r\n      query = query.eq('state_slug', state_slug);\r\n    }\r\n    if (locality_slug) {\r\n      query = query.eq('locality_slug', locality_slug);\r\n    }\r\n\r\n    const { data, error } = await query.limit(1);\r\n\r\n    if (error) {\r\n      console.error('Error fetching address data:', error);\r\n      return null;\r\n    }\r\n\r\n    if (!data || data.length === 0) {\r\n      // Fallback: try with just pincode\r\n      const { data: fallbackData, error: fallbackError } = await supabase\r\n        .from('pincodes')\r\n        .select('OfficeName, DivisionName, StateName, Pincode')\r\n        .eq('Pincode', pincode)\r\n        .limit(1);\r\n\r\n      if (fallbackError || !fallbackData || fallbackData.length === 0) {\r\n        return null;\r\n      }\r\n\r\n      const record = fallbackData[0];\r\n      return {\r\n        locality: record.OfficeName || '',\r\n        city: record.DivisionName || '',\r\n        state: record.StateName || '',\r\n        pincode: record.Pincode || '',\r\n      };\r\n    }\r\n\r\n    const record = data[0];\r\n    return {\r\n      locality: record.OfficeName || '',\r\n      city: record.DivisionName || '',\r\n      state: record.StateName || '',\r\n      pincode: record.Pincode || '',\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in fetchPostAddress:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Format address parts into a readable string\r\n */\r\nexport function formatAddressString(address: PostAddress): string {\r\n  const parts = [];\r\n  \r\n  if (address.locality) parts.push(address.locality);\r\n  if (address.city) parts.push(address.city);\r\n  if (address.state) parts.push(address.state);\r\n  if (address.pincode) parts.push(address.pincode);\r\n  \r\n  return parts.join(', ');\r\n}\r\n\r\n/**\r\n * Fallback function to format address from slugs (for when API fails)\r\n */\r\nexport function formatAddressFromSlugs(\r\n  locality_slug?: string | null,\r\n  city_slug?: string | null,\r\n  state_slug?: string | null,\r\n  pincode?: string | null\r\n): string {\r\n  const addressParts = [];\r\n  \r\n  if (locality_slug) {\r\n    addressParts.push(locality_slug.replace(/-/g, ' '));\r\n  }\r\n  if (city_slug) {\r\n    addressParts.push(city_slug.replace(/-/g, ' '));\r\n  }\r\n  if (state_slug) {\r\n    addressParts.push(state_slug.replace(/-/g, ' '));\r\n  }\r\n  if (pincode) {\r\n    addressParts.push(pincode);\r\n  }\r\n  \r\n  return addressParts.join(', ');\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAYO,eAAe,iBACpB,aAA6B,EAC7B,SAAyB,EACzB,UAA0B,EAC1B,OAAuB;IAEvB,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,yBAAyB;QACzB,IAAI,QAAQ,SACT,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,EAAE,CAAC,WAAW;QAEjB,sCAAsC;QACtC,IAAI,WAAW;YACb,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QACA,IAAI,YAAY;YACd,QAAQ,MAAM,EAAE,CAAC,cAAc;QACjC;QACA,IAAI,eAAe;YACjB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,KAAK,CAAC;QAE1C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;QAEA,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;YAC9B,kCAAkC;YAClC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,EAAE,CAAC,WAAW,SACd,KAAK,CAAC;YAET,IAAI,iBAAiB,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;gBAC/D,OAAO;YACT;YAEA,MAAM,SAAS,YAAY,CAAC,EAAE;YAC9B,OAAO;gBACL,UAAU,OAAO,UAAU,IAAI;gBAC/B,MAAM,OAAO,YAAY,IAAI;gBAC7B,OAAO,OAAO,SAAS,IAAI;gBAC3B,SAAS,OAAO,OAAO,IAAI;YAC7B;QACF;QAEA,MAAM,SAAS,IAAI,CAAC,EAAE;QACtB,OAAO;YACL,UAAU,OAAO,UAAU,IAAI;YAC/B,MAAM,OAAO,YAAY,IAAI;YAC7B,OAAO,OAAO,SAAS,IAAI;YAC3B,SAAS,OAAO,OAAO,IAAI;QAC7B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAKO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,QAAQ,EAAE;IAEhB,IAAI,QAAQ,QAAQ,EAAE,MAAM,IAAI,CAAC,QAAQ,QAAQ;IACjD,IAAI,QAAQ,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,IAAI;IACzC,IAAI,QAAQ,KAAK,EAAE,MAAM,IAAI,CAAC,QAAQ,KAAK;IAC3C,IAAI,QAAQ,OAAO,EAAE,MAAM,IAAI,CAAC,QAAQ,OAAO;IAE/C,OAAO,MAAM,IAAI,CAAC;AACpB;AAKO,SAAS,uBACd,aAA6B,EAC7B,SAAyB,EACzB,UAA0B,EAC1B,OAAuB;IAEvB,MAAM,eAAe,EAAE;IAEvB,IAAI,eAAe;QACjB,aAAa,IAAI,CAAC,cAAc,OAAO,CAAC,MAAM;IAChD;IACA,IAAI,WAAW;QACb,aAAa,IAAI,CAAC,UAAU,OAAO,CAAC,MAAM;IAC5C;IACA,IAAI,YAAY;QACd,aAAa,IAAI,CAAC,WAAW,OAAO,CAAC,MAAM;IAC7C;IACA,IAAI,SAAS;QACX,aAAa,IAAI,CAAC;IACpB;IAEA,OAAO,aAAa,IAAI,CAAC;AAC3B", "debugId": null}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/hooks/usePostOwnership.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\nexport interface UsePostOwnershipProps {\r\n  postBusinessId?: string;\r\n}\r\n\r\nexport interface UsePostOwnershipReturn {\r\n  isOwner: boolean;\r\n  isLoading: boolean;\r\n  currentUserId: string | null;\r\n}\r\n\r\n/**\r\n * Hook to determine if the current user owns a specific post\r\n */\r\nexport function usePostOwnership({ postBusinessId }: UsePostOwnershipProps): UsePostOwnershipReturn {\r\n  const [isOwner, setIsOwner] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [currentUserId, setCurrentUserId] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const checkOwnership = async () => {\r\n      try {\r\n        const supabase = createClient();\r\n        const { data: { user }, error } = await supabase.auth.getUser();\r\n\r\n        if (error || !user) {\r\n          setIsOwner(false);\r\n          setCurrentUserId(null);\r\n          setIsLoading(false);\r\n          return;\r\n        }\r\n\r\n        setCurrentUserId(user.id);\r\n        \r\n        // Check if the current user's ID matches the post's business_id\r\n        if (postBusinessId && user.id === postBusinessId) {\r\n          setIsOwner(true);\r\n        } else {\r\n          setIsOwner(false);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error checking post ownership:\", error);\r\n        setIsOwner(false);\r\n        setCurrentUserId(null);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    checkOwnership();\r\n  }, [postBusinessId]);\r\n\r\n  return {\r\n    isOwner,\r\n    isLoading,\r\n    currentUserId,\r\n  };\r\n}\r\n\r\n/**\r\n * Simple utility function to check if user owns a post\r\n */\r\nexport function checkPostOwnership(currentUserId: string | null, postBusinessId: string | undefined): boolean {\r\n  return !!(currentUserId && postBusinessId && currentUserId === postBusinessId);\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;AAHA;;;AAkBO,SAAS,iBAAiB,EAAE,cAAc,EAAyB;;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;6DAAiB;oBACrB,IAAI;wBACF,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;wBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;wBAE7D,IAAI,SAAS,CAAC,MAAM;4BAClB,WAAW;4BACX,iBAAiB;4BACjB,aAAa;4BACb;wBACF;wBAEA,iBAAiB,KAAK,EAAE;wBAExB,gEAAgE;wBAChE,IAAI,kBAAkB,KAAK,EAAE,KAAK,gBAAgB;4BAChD,WAAW;wBACb,OAAO;4BACL,WAAW;wBACb;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,WAAW;wBACX,iBAAiB;oBACnB,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;qCAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;QACA;IACF;AACF;GA3CgB;AAgDT,SAAS,mBAAmB,aAA4B,EAAE,cAAkC;IACjG,OAAO,CAAC,CAAC,CAAC,iBAAiB,kBAAkB,kBAAkB,cAAc;AAC/E", "debugId": null}}, {"offset": {"line": 1540, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/utils.ts"], "sourcesContent": ["/**\r\n * Utility functions for posts\r\n */\r\n\r\nimport { PostData } from '@/lib/types/posts';\r\nimport { UserProfile } from './types';\r\n\r\n/**\r\n * Calculate post score based on base score and time decay\r\n */\r\nexport function calculatePostScore(post: PostData, baseScore: number, _userProfile?: UserProfile): number {\r\n  const now = new Date();\r\n  const postDate = new Date(post.created_at);\r\n  const daysDiff = (now.getTime() - postDate.getTime()) / (1000 * 60 * 60 * 24);\r\n\r\n  let timeDecayFactor = 1.0;\r\n  if (daysDiff <= 1) {\r\n    timeDecayFactor = 1.0;\r\n  } else if (daysDiff <= 7) {\r\n    timeDecayFactor = 0.8;\r\n  } else if (daysDiff <= 28) {\r\n    timeDecayFactor = 0.6;\r\n  } else {\r\n    timeDecayFactor = 0.4;\r\n  }\r\n\r\n  return Math.round(baseScore * timeDecayFactor);\r\n}\r\n\r\n/**\r\n * Get the business post select query with business profile\r\n */\r\nexport function getBusinessPostSelectQuery(): string {\r\n  return `\r\n    *,\r\n    business_profiles!inner (\r\n      id,\r\n      business_name,\r\n      logo_url,\r\n      business_slug,\r\n      phone,\r\n      whatsapp_number,\r\n      city,\r\n      state\r\n    )\r\n  `;\r\n}\r\n\r\n/**\r\n * Build exclusion filter for business IDs\r\n */\r\nexport function buildBusinessIdExclusionFilter(businessIds: string[]): string {\r\n  return businessIds.length > 0 ? `(${businessIds.join(',')})` : 'null';\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAQM,SAAS,mBAAmB,IAAc,EAAE,SAAiB,EAAE,YAA0B;IAC9F,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK,KAAK,UAAU;IACzC,MAAM,WAAW,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAE5E,IAAI,kBAAkB;IACtB,IAAI,YAAY,GAAG;QACjB,kBAAkB;IACpB,OAAO,IAAI,YAAY,GAAG;QACxB,kBAAkB;IACpB,OAAO,IAAI,YAAY,IAAI;QACzB,kBAAkB;IACpB,OAAO;QACL,kBAAkB;IACpB;IAEA,OAAO,KAAK,KAAK,CAAC,YAAY;AAChC;AAKO,SAAS;IACd,OAAO,CAAC;;;;;;;;;;;;EAYR,CAAC;AACH;AAKO,SAAS,+BAA+B,WAAqB;IAClE,OAAO,YAAY,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG;AACjE", "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/index.ts"], "sourcesContent": ["/**\r\n * Main exports for posts actions\r\n * This file serves as the public API for all posts-related functionality\r\n */\r\n\r\n// CRUD operations\r\nexport { createPost, updatePost, updatePostContent, updatePostProducts, deletePost } from './crud';\r\n\r\n// Unified feed functionality (business + customer posts)\r\nexport { getUnifiedFeedPosts, getUnifiedFeedPostsWithAuthors } from './unifiedFeed';\r\nexport type { UnifiedPost, UnifiedFeedResponse } from './unifiedFeed';\r\n\r\n// Utility functions (if needed externally)\r\nexport { calculatePostScore } from './utils';\r\n\r\n// Types (if needed externally)\r\nexport type { UserProfile, PostWithScore } from './types';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,kBAAkB;;AAGlB,yDAAyD;AACzD;AAGA,2CAA2C;AAC3C", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts.ts"], "sourcesContent": ["// This file has been refactored into smaller modules\r\n// Please use the new modular structure in lib/actions/posts/\r\n\r\nexport * from \"./posts/index\";\r\n"], "names": [], "mappings": "AAAA,qDAAqD;AACrD,6DAA6D;;AAE7D", "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/crud.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { PostFormData } from '@/lib/types/posts';\r\nimport { ActionResponse } from '@/lib/types/api';\r\nimport { deletePostMedia } from '@/lib/actions/shared/upload-post-media';\r\n\r\n/**\r\n * Create a new post\r\n */\r\nexport async function createPost(formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !businessProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Business profile not found',\r\n      error: 'You must have a business profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    business_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: businessProfile.city_slug,\r\n    state_slug: businessProfile.state_slug,\r\n    locality_slug: businessProfile.locality_slug,\r\n    pincode: businessProfile.pincode,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || []\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the content of an existing post (for inline editing)\r\n */\r\nexport async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the content and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      content: content.trim(),\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post content:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the product_ids of an existing post (for inline editing)\r\n */\r\nexport async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the product_ids and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      product_ids: productIds,\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post products:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post products',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post products updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing post (full update for form submissions)\r\n */\r\nexport async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a post\r\n */\r\nexport async function deletePost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user, get creation date for media deletion\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id, created_at, image_url')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // Always attempt to delete the post folder from storage\r\n  // This ensures we clean up any files that might exist, regardless of image_url status\r\n  try {\r\n    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);\r\n    if (!mediaDeleteResult.success && mediaDeleteResult.error) {\r\n      console.error('Error deleting post media:', mediaDeleteResult.error);\r\n      // Continue with post deletion even if media deletion fails\r\n    }\r\n  } catch (mediaError) {\r\n    console.error('Error deleting post media:', mediaError);\r\n    // Continue with post deletion even if media deletion fails\r\n  }\r\n\r\n  // Delete the post\r\n  const { error } = await supabase\r\n    .from('business_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAqNsB,aAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\ninterface DialogContentProps extends React.ComponentProps<typeof DialogPrimitive.Content> {\r\n  hideClose?: boolean;\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  hideClose = false,\r\n  ...props\r\n}: DialogContentProps) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        {!hideClose && (\r\n          <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer\">\r\n            <XIcon />\r\n            <span className=\"sr-only\">Close</span>\r\n          </DialogPrimitive.Close>\r\n        )}\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  )\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn(\"text-lg leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAoBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACjB,GAAG,OACgB;IACnB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,CAAC,2BACA,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MA3BS;AA6BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,6LAAC,yIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,6LAAC,8HAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,6LAAC,8HAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,6LAAC,8HAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,6LAAC,8HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,6LAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;;0BAEV,6LAAC,6MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2093, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 2174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/productActions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { ProductData } from '@/lib/types/posts';\r\n\r\n// Search business products for the current user\r\nexport async function searchBusinessProducts(query: string): Promise<{\r\n  success: boolean;\r\n  data?: ProductData[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!query || query.trim().length < 2) {\r\n      return {\r\n        success: false,\r\n        error: 'Search query must be at least 2 characters long'\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: 'Authentication required'\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n\r\n\r\n    // Search products for the current user's business only\r\n    const { data, error } = await supabaseAdmin\r\n      .from('products_services')\r\n      .select('id, name, base_price, discounted_price, image_url, slug')\r\n      .eq('business_id', user.id) // Ensure user can only see their own products\r\n      .eq('is_available', true)\r\n      .ilike('name', `%${query.trim()}%`)\r\n      .order('name', { ascending: true })\r\n      .limit(10); // Limit search results to 10 items\r\n\r\n    if (error) {\r\n      console.error('Error searching products:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Failed to search products'\r\n      };\r\n    }\r\n\r\n\r\n\r\n    return {\r\n      success: true,\r\n      data: data || []\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in searchBusinessProducts:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred'\r\n    };\r\n  }\r\n}\r\n\r\n// Get selected products by IDs for the current user\r\nexport async function getSelectedProducts(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductData[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!productIds || productIds.length === 0) {\r\n      return {\r\n        success: true,\r\n        data: []\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: 'Authentication required'\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Get products by IDs, but only for the current user's business\r\n    const { data, error } = await supabaseAdmin\r\n      .from('products_services')\r\n      .select('id, name, base_price, discounted_price, image_url, slug')\r\n      .in('id', productIds)\r\n      .eq('business_id', user.id) // Ensure user can only access their own products\r\n      .order('name', { ascending: true });\r\n\r\n    if (error) {\r\n      console.error('Error getting selected products:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Failed to get selected products'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || []\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in getSelectedProducts:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred'\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAOsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2190, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/productActions.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { ProductData } from '@/lib/types/posts';\r\n\r\n// Search business products for the current user\r\nexport async function searchBusinessProducts(query: string): Promise<{\r\n  success: boolean;\r\n  data?: ProductData[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!query || query.trim().length < 2) {\r\n      return {\r\n        success: false,\r\n        error: 'Search query must be at least 2 characters long'\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: 'Authentication required'\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n\r\n\r\n    // Search products for the current user's business only\r\n    const { data, error } = await supabaseAdmin\r\n      .from('products_services')\r\n      .select('id, name, base_price, discounted_price, image_url, slug')\r\n      .eq('business_id', user.id) // Ensure user can only see their own products\r\n      .eq('is_available', true)\r\n      .ilike('name', `%${query.trim()}%`)\r\n      .order('name', { ascending: true })\r\n      .limit(10); // Limit search results to 10 items\r\n\r\n    if (error) {\r\n      console.error('Error searching products:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Failed to search products'\r\n      };\r\n    }\r\n\r\n\r\n\r\n    return {\r\n      success: true,\r\n      data: data || []\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in searchBusinessProducts:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred'\r\n    };\r\n  }\r\n}\r\n\r\n// Get selected products by IDs for the current user\r\nexport async function getSelectedProducts(productIds: string[]): Promise<{\r\n  success: boolean;\r\n  data?: ProductData[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Validate input\r\n    if (!productIds || productIds.length === 0) {\r\n      return {\r\n        success: true,\r\n        data: []\r\n      };\r\n    }\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: 'Authentication required'\r\n      };\r\n    }\r\n\r\n    // Use admin client to bypass RLS policies\r\n    const supabaseAdmin = createAdminClient();\r\n\r\n    // Get products by IDs, but only for the current user's business\r\n    const { data, error } = await supabaseAdmin\r\n      .from('products_services')\r\n      .select('id, name, base_price, discounted_price, image_url, slug')\r\n      .in('id', productIds)\r\n      .eq('business_id', user.id) // Ensure user can only access their own products\r\n      .order('name', { ascending: true });\r\n\r\n    if (error) {\r\n      console.error('Error getting selected products:', error);\r\n      return {\r\n        success: false,\r\n        error: 'Failed to get selected products'\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      data: data || []\r\n    };\r\n  } catch (error) {\r\n    console.error('Error in getSelectedProducts:', error);\r\n    return {\r\n      success: false,\r\n      error: 'An unexpected error occurred'\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAuEsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/forms/ProductSelector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback, useRef } from 'react';\r\nimport { Check, ChevronsUpDown, Loader2, X, Package, Search, GripVertical } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport {\r\n  DndContext,\r\n  closestCenter,\r\n  KeyboardSensor,\r\n  PointerSensor,\r\n  useSensor,\r\n  useSensors,\r\n  DragEndEvent,\r\n} from '@dnd-kit/core';\r\nimport {\r\n  arrayMove,\r\n  SortableContext,\r\n  sortableKeyboardCoordinates,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport {\r\n  useSortable,\r\n} from '@dnd-kit/sortable';\r\nimport { CSS } from '@dnd-kit/utilities';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from '@/components/ui/command';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { ProductData } from '@/lib/types/posts';\r\nimport { searchBusinessProducts, getSelectedProducts } from '@/lib/actions/shared/productActions';\r\n\r\ninterface ProductSelectorProps {\r\n  selectedProductIds: string[];\r\n  onProductsChange: (_productIds: string[]) => void;\r\n}\r\n\r\n// Sortable Product Item Component\r\ninterface SortableProductItemProps {\r\n  product: ProductData;\r\n  onRemove: (_productId: string) => void;\r\n  formatPrice: (_price: number | null) => string;\r\n}\r\n\r\nfunction SortableProductItem({ product, onRemove, formatPrice }: SortableProductItemProps) {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({ id: product.id });\r\n\r\n  const style = {\r\n    transform: CSS.Transform.toString(transform),\r\n    transition,\r\n    opacity: isDragging ? 0.5 : 1,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className=\"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-muted/50 rounded-lg border min-h-[60px] sm:min-h-[68px]\"\r\n    >\r\n      {/* Drag Handle */}\r\n      <div\r\n        {...attributes}\r\n        {...listeners}\r\n        className=\"cursor-grab active:cursor-grabbing text-muted-foreground hover:text-foreground shrink-0\"\r\n      >\r\n        <GripVertical className=\"h-4 w-4\" />\r\n      </div>\r\n\r\n      {/* Product Image */}\r\n      <div className=\"relative h-8 w-8 sm:h-10 sm:w-10 shrink-0 rounded-md overflow-hidden bg-background\">\r\n        {product.image_url ? (\r\n          <Image\r\n            src={product.image_url}\r\n            alt={product.name}\r\n            fill\r\n            className=\"object-cover\"\r\n            sizes=\"(max-width: 640px) 32px, 40px\"\r\n          />\r\n        ) : (\r\n          <div className=\"flex items-center justify-center h-full w-full\">\r\n            <Package className=\"h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground\" />\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Product Details */}\r\n      <div className=\"flex-1 min-w-0 pr-1 sm:pr-2\">\r\n        {/* Product Name - Truncated to single line for consistent height */}\r\n        <div className=\"font-medium text-xs sm:text-sm leading-tight mb-1\">\r\n          <span className=\"line-clamp-1 break-words\">\r\n            {product.name}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Price - Single line layout */}\r\n        <div className=\"text-xs text-muted-foreground\">\r\n          {product.discounted_price ? (\r\n            <div className=\"flex items-center gap-1 flex-wrap\">\r\n              <span className=\"text-primary font-medium\">\r\n                {formatPrice(product.discounted_price)}\r\n              </span>\r\n              <span className=\"line-through text-xs\">\r\n                {formatPrice(product.base_price)}\r\n              </span>\r\n            </div>\r\n          ) : (\r\n            <span className=\"font-medium\">\r\n              {formatPrice(product.base_price)}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Remove Button */}\r\n      <Button\r\n        variant=\"ghost\"\r\n        size=\"icon\"\r\n        className=\"h-6 w-6 sm:h-8 sm:w-8 shrink-0 hover:bg-destructive/10 hover:text-destructive\"\r\n        onClick={() => onRemove(product.id)}\r\n      >\r\n        <X className=\"h-3 w-3 sm:h-4 sm:w-4\" />\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function ProductSelector({\r\n  selectedProductIds,\r\n  onProductsChange,\r\n}: ProductSelectorProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchValue, setSearchValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [products, setProducts] = useState<ProductData[]>([]);\r\n  const [selectedProducts, setSelectedProducts] = useState<ProductData[]>([]);\r\n  const [hasSearched, setHasSearched] = useState(false);\r\n\r\n  // Debounce timer ref\r\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Drag and drop sensors\r\n  const sensors = useSensors(\r\n    useSensor(PointerSensor),\r\n    useSensor(KeyboardSensor, {\r\n      coordinateGetter: sortableKeyboardCoordinates,\r\n    })\r\n  );\r\n\r\n  // Handle drag end\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (active.id !== over?.id) {\r\n      const oldIndex = selectedProducts.findIndex((product) => product.id === active.id);\r\n      const newIndex = selectedProducts.findIndex((product) => product.id === over?.id);\r\n\r\n      if (oldIndex !== -1 && newIndex !== -1) {\r\n        const newSelectedProducts = arrayMove(selectedProducts, oldIndex, newIndex);\r\n        setSelectedProducts(newSelectedProducts);\r\n\r\n        // Update the product IDs array to maintain the new order\r\n        const newSelectedIds = newSelectedProducts.map(product => product.id);\r\n        onProductsChange(newSelectedIds);\r\n      }\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Load selected products details using server action\r\n  const loadSelectedProducts = useCallback(async () => {\r\n    if (selectedProductIds.length === 0) {\r\n      setSelectedProducts([]);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      const result = await getSelectedProducts(selectedProductIds);\r\n\r\n      if (result.success && result.data) {\r\n        // Maintain the order of selectedProductIds\r\n        const orderedProducts = selectedProductIds\r\n          .map(id => result.data?.find(product => product.id === id))\r\n          .filter(Boolean) as ProductData[];\r\n\r\n        setSelectedProducts(orderedProducts);\r\n      } else {\r\n        console.error('Error loading selected products:', result.error);\r\n        setSelectedProducts([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading selected products:', error);\r\n      setSelectedProducts([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [selectedProductIds]);\r\n\r\n  // Load selected products on mount\r\n  useEffect(() => {\r\n    if (selectedProductIds.length > 0) {\r\n      loadSelectedProducts();\r\n    }\r\n  }, [selectedProductIds, loadSelectedProducts]);\r\n\r\n  // Search products using server action\r\n  const searchProducts = async (query: string) => {\r\n    setIsLoading(true);\r\n    setHasSearched(false);\r\n    try {\r\n      const result = await searchBusinessProducts(query);\r\n\r\n      if (result.success && result.data) {\r\n        setProducts(result.data);\r\n      } else {\r\n        console.error('Error searching products:', result.error);\r\n        setProducts([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error searching products:', error);\r\n      setProducts([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n      setHasSearched(true);\r\n    }\r\n  };\r\n\r\n  // Handle search input change with debouncing\r\n  const handleSearchChange = (value: string) => {\r\n    setSearchValue(value);\r\n\r\n    // Clear existing timer\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    // Clear products if search is too short\r\n    if (value.length < 2) {\r\n      setProducts([]);\r\n      setHasSearched(false);\r\n      return;\r\n    }\r\n\r\n    // Set new timer for debounced search\r\n    debounceTimerRef.current = setTimeout(() => {\r\n      searchProducts(value);\r\n    }, 300); // 300ms debounce delay\r\n  };\r\n\r\n  // Cleanup debounce timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimerRef.current) {\r\n        clearTimeout(debounceTimerRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Toggle product selection\r\n  const toggleProduct = (product: ProductData) => {\r\n    const isSelected = selectedProductIds.includes(product.id);\r\n    let newSelectedIds: string[];\r\n\r\n    if (isSelected) {\r\n      newSelectedIds = selectedProductIds.filter(id => id !== product.id);\r\n      setSelectedProducts(prev => prev.filter(p => p.id !== product.id));\r\n    } else {\r\n      // Check if maximum limit is reached\r\n      if (selectedProductIds.length >= 5) {\r\n        return; // Don't add more products if limit is reached\r\n      }\r\n      newSelectedIds = [...selectedProductIds, product.id];\r\n      setSelectedProducts(prev => [...prev, product]);\r\n    }\r\n\r\n    onProductsChange(newSelectedIds);\r\n  };\r\n\r\n  // Remove a selected product\r\n  const removeProduct = (productId: string) => {\r\n    const newSelectedIds = selectedProductIds.filter(id => id !== productId);\r\n    setSelectedProducts(prev => prev.filter(p => p.id !== productId));\r\n    onProductsChange(newSelectedIds);\r\n  };\r\n\r\n  // Format price display\r\n  const formatPrice = (price: number | null) => {\r\n    if (price === null) return 'N/A';\r\n    return `₹${price.toLocaleString('en-IN')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Product Search Combobox */}\r\n      <Popover open={open} onOpenChange={(newOpen) => {\r\n        setOpen(newOpen);\r\n        if (!newOpen) {\r\n          // Clear search when closing\r\n          setSearchValue('');\r\n          setProducts([]);\r\n          setHasSearched(false);\r\n        }\r\n      }}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            role=\"combobox\"\r\n            aria-expanded={open}\r\n            className=\"w-full justify-between h-auto min-h-[40px] px-3 py-2\"\r\n          >\r\n            <div className=\"flex items-center gap-2\">\r\n              <Search className=\"h-4 w-4 text-muted-foreground\" />\r\n              <span className=\"text-left text-muted-foreground\">\r\n                Search and add products...\r\n              </span>\r\n            </div>\r\n            <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent\r\n          className=\"p-0\"\r\n          align=\"start\"\r\n          sideOffset={4}\r\n          style={{ width: 'var(--radix-popover-trigger-width)' }}\r\n        >\r\n          <Command>\r\n            <CommandInput\r\n              placeholder=\"Search your products...\"\r\n              value={searchValue}\r\n              onValueChange={handleSearchChange}\r\n              className=\"h-9 border-0 focus:ring-0 focus:ring-offset-0\"\r\n            />\r\n            <CommandList className=\"max-h-[300px]\">\r\n              {/* Show loading state */}\r\n              {isLoading && (\r\n                <div className=\"flex items-center justify-center py-8\">\r\n                  <div className=\"flex flex-col items-center gap-2\">\r\n                    <Loader2 className=\"h-6 w-6 animate-spin text-primary\" />\r\n                    <span className=\"text-sm text-muted-foreground\">Searching products...</span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Show empty state only when not loading and no products */}\r\n              {!isLoading && products.length === 0 && (\r\n                <CommandEmpty>\r\n                  <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n                    <Package className=\"h-8 w-8 text-muted-foreground mb-2\" />\r\n                    <span className=\"text-sm font-medium\">\r\n                      {searchValue.length < 2\r\n                        ? 'Type at least 2 characters to search'\r\n                        : hasSearched\r\n                        ? 'No products found'\r\n                        : ''}\r\n                    </span>\r\n                    {searchValue.length >= 2 && hasSearched && (\r\n                      <span className=\"text-xs text-muted-foreground mt-1\">\r\n                        Try a different search term\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </CommandEmpty>\r\n              )}\r\n\r\n              {/* Show products only when not loading and we have products */}\r\n              {!isLoading && products.length > 0 && (\r\n                <CommandGroup>\r\n                  {products.map((product) => (\r\n                  <CommandItem\r\n                    key={product.id}\r\n                    value={product.slug || product.id}\r\n                    onSelect={() => {\r\n                      // Only allow selection if not already at max limit or if already selected\r\n                      if (selectedProductIds.length < 5 || selectedProductIds.includes(product.id)) {\r\n                        toggleProduct(product);\r\n                        setOpen(false);\r\n                        setSearchValue('');\r\n                        setProducts([]);\r\n                        setHasSearched(false);\r\n                      }\r\n                    }}\r\n                    disabled={selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)}\r\n                    className={cn(\r\n                      \"flex items-center gap-3 p-3 cursor-pointer\",\r\n                      selectedProductIds.length >= 5 && !selectedProductIds.includes(product.id)\r\n                        ? \"opacity-50 cursor-not-allowed\"\r\n                        : \"\"\r\n                    )}\r\n                  >\r\n                    {/* Product Image */}\r\n                    <div className=\"relative h-10 w-10 shrink-0 rounded-md overflow-hidden bg-muted\">\r\n                      {product.image_url ? (\r\n                        <Image\r\n                          src={product.image_url}\r\n                          alt={product.name}\r\n                          fill\r\n                          className=\"object-cover\"\r\n                          sizes=\"40px\"\r\n                        />\r\n                      ) : (\r\n                        <div className=\"flex items-center justify-center h-full w-full\">\r\n                          <Package className=\"h-5 w-5 text-muted-foreground\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Product Details */}\r\n                    <div className=\"flex-1 min-w-0 pr-2\">\r\n                      <div className=\"font-medium text-sm truncate mb-1\">{product.name}</div>\r\n                      <div className=\"text-xs text-muted-foreground\">\r\n                        {product.discounted_price ? (\r\n                          <div className=\"flex items-center gap-1 flex-wrap\">\r\n                            <span className=\"text-primary font-medium\">\r\n                              {formatPrice(product.discounted_price)}\r\n                            </span>\r\n                            <span className=\"line-through text-xs\">\r\n                              {formatPrice(product.base_price)}\r\n                            </span>\r\n                          </div>\r\n                        ) : (\r\n                          <span className=\"font-medium\">\r\n                            {formatPrice(product.base_price)}\r\n                          </span>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Check Icon */}\r\n                    <Check\r\n                      className={cn(\r\n                        \"ml-auto h-4 w-4\",\r\n                        selectedProductIds.includes(product.id)\r\n                          ? \"opacity-100 text-primary\"\r\n                          : \"opacity-0\"\r\n                      )}\r\n                    />\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              )}\r\n            </CommandList>\r\n          </Command>\r\n        </PopoverContent>\r\n      </Popover>\r\n\r\n      {/* Selected products with drag and drop */}\r\n      <div className=\"space-y-3\">\r\n        {/* Show loading state when loading selected products */}\r\n        {isLoading && selectedProductIds.length > 0 && selectedProducts.length === 0 && (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center gap-2 text-sm font-medium text-foreground\">\r\n              <Package className=\"h-4 w-4\" />\r\n              <span>Loading Selected Products...</span>\r\n            </div>\r\n            <div className=\"flex justify-center py-6\">\r\n              <div className=\"flex flex-col items-center gap-2\">\r\n                <Loader2 className=\"h-6 w-6 animate-spin text-[var(--brand-gold)]\" />\r\n                <span className=\"text-xs text-muted-foreground\">Fetching product details...</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Show selected products when loaded */}\r\n        {selectedProducts.length > 0 && (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center gap-2 text-sm font-medium text-foreground\">\r\n              <Package className=\"h-4 w-4\" />\r\n              <span>Selected Products</span>\r\n              <span className=\"text-xs text-muted-foreground font-normal\">\r\n                (Drag to reorder)\r\n              </span>\r\n            </div>\r\n            <DndContext\r\n              sensors={sensors}\r\n              collisionDetection={closestCenter}\r\n              onDragEnd={handleDragEnd}\r\n            >\r\n              <SortableContext\r\n                items={selectedProducts.map(p => p.id)}\r\n                strategy={verticalListSortingStrategy}\r\n              >\r\n                <div className=\"grid gap-2\">\r\n                  {selectedProducts.map((product) => (\r\n                    <SortableProductItem\r\n                      key={product.id}\r\n                      product={product}\r\n                      onRemove={removeProduct}\r\n                      formatPrice={formatPrice}\r\n                    />\r\n                  ))}\r\n                </div>\r\n              </SortableContext>\r\n            </DndContext>\r\n          </div>\r\n        )}\r\n\r\n        {/* Product limit indicator */}\r\n        <div className=\"flex justify-between items-center text-xs\">\r\n          <span className=\"text-muted-foreground\">\r\n            {selectedProductIds.length === 0\r\n              ? \"No products selected\"\r\n              : `${selectedProductIds.length} product${selectedProductIds.length !== 1 ? 's' : ''} selected`}\r\n          </span>\r\n          <div className=\"flex items-center gap-1\">\r\n            <span className={cn(\r\n              \"font-medium\",\r\n              selectedProductIds.length >= 5 ? \"text-destructive\" : \"text-muted-foreground\"\r\n            )}>\r\n              {selectedProductIds.length}/5\r\n            </span>\r\n            <span className=\"text-muted-foreground\">max</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Warning message when limit is reached */}\r\n        {selectedProductIds.length >= 5 && (\r\n          <div className=\"flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded-md\">\r\n            <Package className=\"h-4 w-4 shrink-0\" />\r\n            <span className=\"text-xs font-medium\">\r\n              Maximum limit of 5 products reached\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AASA;AASA;AACA;AACA;AAQA;AAMA;AAEA;AAAA;;;AAzCA;;;;;;;;;;;;;AAuDA,SAAS,oBAAoB,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAA4B;;IACvF,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;QAAE,IAAI,QAAQ,EAAE;IAAC;IAEjC,MAAM,QAAQ;QACZ,WAAW,wKAAA,CAAA,MAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QAClC;QACA,SAAS,aAAa,MAAM;IAC9B;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,WAAU;;0BAGV,6LAAC;gBACE,GAAG,UAAU;gBACb,GAAG,SAAS;gBACb,WAAU;0BAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;;;;;;0BAI1B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,SAAS,iBAChB,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,QAAQ,SAAS;oBACtB,KAAK,QAAQ,IAAI;oBACjB,IAAI;oBACJ,WAAU;oBACV,OAAM;;;;;yCAGR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAMzB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCACb,QAAQ,IAAI;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,gBAAgB,iBACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CACb,YAAY,QAAQ,gBAAgB;;;;;;8CAEvC,6LAAC;oCAAK,WAAU;8CACb,YAAY,QAAQ,UAAU;;;;;;;;;;;iDAInC,6LAAC;4BAAK,WAAU;sCACb,YAAY,QAAQ,UAAU;;;;;;;;;;;;;;;;;0BAOvC,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,SAAS,IAAM,SAAS,QAAQ,EAAE;0BAElC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GAvFS;;QAQH,sKAAA,CAAA,cAAW;;;KARR;AAyFM,SAAS,gBAAgB,EACtC,kBAAkB,EAClB,gBAAgB,EACK;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,wBAAwB;IACxB,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,gBAAa,GACvB,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc,EAAE;QACxB,kBAAkB,sKAAA,CAAA,8BAA2B;IAC/C;IAGF,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,OAAO,EAAE,KAAK,MAAM,IAAI;YAC1B,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,OAAO,EAAE;YACjF,MAAM,WAAW,iBAAiB,SAAS,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,MAAM;YAE9E,IAAI,aAAa,CAAC,KAAK,aAAa,CAAC,GAAG;gBACtC,MAAM,sBAAsB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,UAAU;gBAClE,oBAAoB;gBAEpB,yDAAyD;gBACzD,MAAM,iBAAiB,oBAAoB,GAAG,CAAC,CAAA,UAAW,QAAQ,EAAE;gBACpE,iBAAiB;YACnB;QACF;IACF;IAIA,qDAAqD;IACrD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACvC,IAAI,mBAAmB,MAAM,KAAK,GAAG;gBACnC,oBAAoB,EAAE;gBACtB;YACF;YAEA,aAAa;YACb,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE;gBAEzC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,2CAA2C;oBAC3C,MAAM,kBAAkB,mBACrB,GAAG;6FAAC,CAAA,KAAM,OAAO,IAAI,EAAE;qGAAK,CAAA,UAAW,QAAQ,EAAE,KAAK;;4FACtD,MAAM,CAAC;oBAEV,oBAAoB;gBACtB,OAAO;oBACL,QAAQ,KAAK,CAAC,oCAAoC,OAAO,KAAK;oBAC9D,oBAAoB,EAAE;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,oBAAoB,EAAE;YACxB,SAAU;gBACR,aAAa;YACf;QACF;4DAAG;QAAC;KAAmB;IAEvB,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,mBAAmB,MAAM,GAAG,GAAG;gBACjC;YACF;QACF;oCAAG;QAAC;QAAoB;KAAqB;IAE7C,sCAAsC;IACtC,MAAM,iBAAiB,OAAO;QAC5B,aAAa;QACb,eAAe;QACf,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,yBAAsB,AAAD,EAAE;YAE5C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,YAAY,OAAO,IAAI;YACzB,OAAO;gBACL,QAAQ,KAAK,CAAC,6BAA6B,OAAO,KAAK;gBACvD,YAAY,EAAE;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,YAAY,EAAE;QAChB,SAAU;YACR,aAAa;YACb,eAAe;QACjB;IACF;IAEA,6CAA6C;IAC7C,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QAEf,uBAAuB;QACvB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,wCAAwC;QACxC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,YAAY,EAAE;YACd,eAAe;YACf;QACF;QAEA,qCAAqC;QACrC,iBAAiB,OAAO,GAAG,WAAW;YACpC,eAAe;QACjB,GAAG,MAAM,uBAAuB;IAClC;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;6CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;oCAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,mBAAmB,QAAQ,CAAC,QAAQ,EAAE;QACzD,IAAI;QAEJ,IAAI,YAAY;YACd,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO,QAAQ,EAAE;YAClE,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,EAAE;QAClE,OAAO;YACL,oCAAoC;YACpC,IAAI,mBAAmB,MAAM,IAAI,GAAG;gBAClC,QAAQ,8CAA8C;YACxD;YACA,iBAAiB;mBAAI;gBAAoB,QAAQ,EAAE;aAAC;YACpD,oBAAoB,CAAA,OAAQ;uBAAI;oBAAM;iBAAQ;QAChD;QAEA,iBAAiB;IACnB;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,MAAM,iBAAiB,mBAAmB,MAAM,CAAC,CAAA,KAAM,OAAO;QAC9D,oBAAoB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACtD,iBAAiB;IACnB;IAEA,uBAAuB;IACvB,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,MAAM,OAAO;QAC3B,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,UAAU;IAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,+HAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,cAAc,CAAC;oBAClC,QAAQ;oBACR,IAAI,CAAC,SAAS;wBACZ,4BAA4B;wBAC5B,eAAe;wBACf,YAAY,EAAE;wBACd,eAAe;oBACjB;gBACF;;kCACE,6LAAC,+HAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,iBAAe;4BACf,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAIpD,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+HAAA,CAAA,iBAAc;wBACb,WAAU;wBACV,OAAM;wBACN,YAAY;wBACZ,OAAO;4BAAE,OAAO;wBAAqC;kCAErD,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,eAAY;oCACX,aAAY;oCACZ,OAAO;oCACP,eAAe;oCACf,WAAU;;;;;;8CAEZ,6LAAC,+HAAA,CAAA,cAAW;oCAAC,WAAU;;wCAEpB,2BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;;;;;;;;;;;;wCAMrD,CAAC,aAAa,SAAS,MAAM,KAAK,mBACjC,6LAAC,+HAAA,CAAA,eAAY;sDACX,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC;wDAAK,WAAU;kEACb,YAAY,MAAM,GAAG,IAClB,yCACA,cACA,sBACA;;;;;;oDAEL,YAAY,MAAM,IAAI,KAAK,6BAC1B,6LAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;wCAS5D,CAAC,aAAa,SAAS,MAAM,GAAG,mBAC/B,6LAAC,+HAAA,CAAA,eAAY;sDACV,SAAS,GAAG,CAAC,CAAC,wBACf,6LAAC,+HAAA,CAAA,cAAW;oDAEV,OAAO,QAAQ,IAAI,IAAI,QAAQ,EAAE;oDACjC,UAAU;wDACR,0EAA0E;wDAC1E,IAAI,mBAAmB,MAAM,GAAG,KAAK,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,GAAG;4DAC5E,cAAc;4DACd,QAAQ;4DACR,eAAe;4DACf,YAAY,EAAE;4DACd,eAAe;wDACjB;oDACF;oDACA,UAAU,mBAAmB,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,QAAQ,EAAE;oDACnF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8CACA,mBAAmB,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,IACrE,kCACA;;sEAIN,6LAAC;4DAAI,WAAU;sEACZ,QAAQ,SAAS,iBAChB,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK,QAAQ,SAAS;gEACtB,KAAK,QAAQ,IAAI;gEACjB,IAAI;gEACJ,WAAU;gEACV,OAAM;;;;;qFAGR,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAMzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC,QAAQ,IAAI;;;;;;8EAChE,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,gBAAgB,iBACvB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,YAAY,QAAQ,gBAAgB;;;;;;0FAEvC,6LAAC;gFAAK,WAAU;0FACb,YAAY,QAAQ,UAAU;;;;;;;;;;;6FAInC,6LAAC;wEAAK,WAAU;kFACb,YAAY,QAAQ,UAAU;;;;;;;;;;;;;;;;;sEAOvC,6LAAC,uMAAA,CAAA,QAAK;4DACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mBACA,mBAAmB,QAAQ,CAAC,QAAQ,EAAE,IAClC,6BACA;;;;;;;mDAhEH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA6E7B,6LAAC;gBAAI,WAAU;;oBAEZ,aAAa,mBAAmB,MAAM,GAAG,KAAK,iBAAiB,MAAM,KAAK,mBACzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;oBAOvD,iBAAiB,MAAM,GAAG,mBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAI9D,6LAAC,8JAAA,CAAA,aAAU;gCACT,SAAS;gCACT,oBAAoB,8JAAA,CAAA,gBAAa;gCACjC,WAAW;0CAEX,cAAA,6LAAC,sKAAA,CAAA,kBAAe;oCACd,OAAO,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;oCACrC,UAAU,sKAAA,CAAA,8BAA2B;8CAErC,cAAA,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;gDAEC,SAAS;gDACT,UAAU;gDACV,aAAa;+CAHR,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAa7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,mBAAmB,MAAM,KAAK,IAC3B,yBACA,GAAG,mBAAmB,MAAM,CAAC,QAAQ,EAAE,mBAAmB,MAAM,KAAK,IAAI,MAAM,GAAG,SAAS,CAAC;;;;;;0CAElG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAChB,eACA,mBAAmB,MAAM,IAAI,IAAI,qBAAqB;;4CAErD,mBAAmB,MAAM;4CAAC;;;;;;;kDAE7B,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAK3C,mBAAmB,MAAM,IAAI,mBAC5B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;IAlZwB;;QAeN,8JAAA,CAAA,aAAU;;;MAfJ", "debugId": null}}, {"offset": {"line": 3093, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/editors/InlinePostAndProductEditor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Loader2, Check, X, Package } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { updatePost } from \"@/lib/actions/posts\";\r\nimport ProductSelector from \"@/components/feed/shared/forms/ProductSelector\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nexport interface InlinePostAndProductEditorProps {\r\n  postId: string;\r\n  initialContent: string;\r\n  initialProductIds: string[];\r\n  initialImageUrl?: string | null;\r\n  onSave?: (newContent: string, newProductIds: string[]) => void;\r\n  onCancel?: () => void;\r\n  className?: string;\r\n}\r\n\r\nexport default function InlinePostAndProductEditor({\r\n  postId,\r\n  initialContent,\r\n  initialProductIds,\r\n  initialImageUrl,\r\n  onSave,\r\n  onCancel,\r\n  className = \"\",\r\n}: InlinePostAndProductEditorProps) {\r\n  const [content, setContent] = useState(initialContent);\r\n  const [productIds, setProductIds] = useState<string[]>(initialProductIds);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [charCount, setCharCount] = useState(initialContent.length);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  const maxChars = 2000;\r\n  const isOverLimit = charCount > maxChars;\r\n  const hasContentChanges = content.trim() !== initialContent.trim();\r\n  const hasProductChanges = JSON.stringify(productIds.sort()) !== JSON.stringify(initialProductIds.sort());\r\n  const hasChanges = hasContentChanges || hasProductChanges;\r\n\r\n  // Focus textarea when component mounts\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.focus();\r\n      // Set cursor to end of text\r\n      textareaRef.current.setSelectionRange(content.length, content.length);\r\n    }\r\n  }, [content.length]);\r\n\r\n  // Handle content change\r\n  const handleContentChange = (value: string) => {\r\n    setContent(value);\r\n    setCharCount(value.length);\r\n  };\r\n\r\n  // Handle save\r\n  const handleSave = async () => {\r\n    if (!hasChanges) {\r\n      onCancel?.();\r\n      return;\r\n    }\r\n\r\n    if (isOverLimit) {\r\n      toast.error(\"Content too long\", {\r\n        description: `Please reduce content to ${maxChars} characters or less.`\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (content.trim().length === 0 && productIds.length === 0) {\r\n      toast.error(\"Content or products required\", {\r\n        description: \"Post must have either content or linked products.\"\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const result = await updatePost(postId, {\r\n        content: content.trim(),\r\n        product_ids: productIds,\r\n        image_url: initialImageUrl,\r\n        mentioned_business_ids: []\r\n      });\r\n\r\n      if (result.success) {\r\n        toast.success(\"Post updated successfully\");\r\n        onSave?.(content.trim(), productIds);\r\n      } else {\r\n        toast.error(\"Failed to update post\", {\r\n          description: result.error || \"Please try again.\"\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating post:\", error);\r\n      toast.error(\"An unexpected error occurred\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle cancel\r\n  const handleCancel = () => {\r\n    setContent(initialContent);\r\n    setCharCount(initialContent.length);\r\n    setProductIds(initialProductIds);\r\n    onCancel?.();\r\n  };\r\n\r\n  // Handle keyboard shortcuts\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Escape') {\r\n      handleCancel();\r\n    } else if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {\r\n      e.preventDefault();\r\n      handleSave();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-4 ${className}`}>\r\n      {/* Content Editor */}\r\n      <div className=\"space-y-3\">\r\n        <div className=\"relative\">\r\n          <Textarea\r\n            ref={textareaRef}\r\n            value={content}\r\n            onChange={(e) => handleContentChange(e.target.value)}\r\n            onKeyDown={handleKeyDown}\r\n            placeholder=\"What's on your mind?\"\r\n            className={`min-h-[100px] resize-none ${\r\n              isOverLimit ? \"border-destructive focus:border-destructive\" : \"\"\r\n            }`}\r\n            disabled={isLoading}\r\n          />\r\n\r\n          {/* Character count */}\r\n          <div className={`absolute bottom-2 right-2 text-xs ${\r\n            isOverLimit ? \"text-destructive\" : \"text-muted-foreground\"\r\n          }`}>\r\n            {charCount}/{maxChars}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Product Selector */}\r\n      <div className=\"space-y-2\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n          <span className=\"text-sm font-medium text-muted-foreground\">\r\n            Linked Products ({productIds.length})\r\n          </span>\r\n        </div>\r\n        <ProductSelector\r\n          selectedProductIds={productIds}\r\n          onProductsChange={setProductIds}\r\n        />\r\n      </div>\r\n\r\n      {/* Action Buttons */}\r\n      <div className=\"flex items-center justify-end gap-2 pt-2\">\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"sm\"\r\n          onClick={handleCancel}\r\n          disabled={isLoading}\r\n          className=\"text-muted-foreground hover:text-foreground\"\r\n        >\r\n          <X className=\"h-4 w-4 mr-1\" />\r\n          Cancel\r\n        </Button>\r\n\r\n        <motion.div\r\n          whileHover={{ scale: hasChanges && !isLoading ? 1.02 : 1 }}\r\n          whileTap={{ scale: hasChanges && !isLoading ? 0.98 : 1 }}\r\n        >\r\n          <Button\r\n            size=\"sm\"\r\n            onClick={handleSave}\r\n            disabled={!hasChanges || isOverLimit || isLoading}\r\n            className=\"relative overflow-hidden\"\r\n            style={{\r\n              background: hasChanges && !isLoading\r\n                ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\r\n                : undefined,\r\n              boxShadow: hasChanges && !isLoading\r\n                ? '0 4px 20px rgba(59, 130, 246, 0.3)'\r\n                : '0 4px 20px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)'\r\n            }}\r\n          >\r\n            <AnimatePresence mode=\"wait\">\r\n              {isLoading ? (\r\n                <motion.div\r\n                  key=\"saving\"\r\n                  initial={{ opacity: 0, x: -10 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  exit={{ opacity: 0, x: 10 }}\r\n                  className=\"flex items-center justify-center\"\r\n                >\r\n                  <Loader2 className=\"h-4 w-4 mr-1 animate-spin\" />\r\n                  Saving...\r\n                </motion.div>\r\n              ) : (\r\n                <motion.div\r\n                  key=\"save\"\r\n                  initial={{ opacity: 0, x: -10 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  exit={{ opacity: 0, x: 10 }}\r\n                  className=\"flex items-center justify-center\"\r\n                >\r\n                  <Check className=\"h-4 w-4 mr-1\" />\r\n                  Save\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n          </Button>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAAA;;;AATA;;;;;;;;;AAqBe,SAAS,2BAA2B,EACjD,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,eAAe,EACf,MAAM,EACN,QAAQ,EACR,YAAY,EAAE,EACkB;;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,MAAM;IAChE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,MAAM,WAAW;IACjB,MAAM,cAAc,YAAY;IAChC,MAAM,oBAAoB,QAAQ,IAAI,OAAO,eAAe,IAAI;IAChE,MAAM,oBAAoB,KAAK,SAAS,CAAC,WAAW,IAAI,QAAQ,KAAK,SAAS,CAAC,kBAAkB,IAAI;IACrG,MAAM,aAAa,qBAAqB;IAExC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK;gBACzB,4BAA4B;gBAC5B,YAAY,OAAO,CAAC,iBAAiB,CAAC,QAAQ,MAAM,EAAE,QAAQ,MAAM;YACtE;QACF;+CAAG;QAAC,QAAQ,MAAM;KAAC;IAEnB,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,aAAa,MAAM,MAAM;IAC3B;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;YACf;YACA;QACF;QAEA,IAAI,aAAa;YACf,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oBAAoB;gBAC9B,aAAa,CAAC,yBAAyB,EAAE,SAAS,oBAAoB,CAAC;YACzE;YACA;QACF;QAEA,IAAI,QAAQ,IAAI,GAAG,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,GAAG;YAC1D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;YACA;QACF;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;gBACtC,SAAS,QAAQ,IAAI;gBACrB,aAAa;gBACb,WAAW;gBACX,wBAAwB,EAAE;YAC5B;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,SAAS,QAAQ,IAAI,IAAI;YAC3B,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,WAAW;QACX,aAAa,eAAe,MAAM;QAClC,cAAc;QACd;IACF;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,GAAG;YACxD,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,WAAQ;4BACP,KAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAW;4BACX,aAAY;4BACZ,WAAW,CAAC,0BAA0B,EACpC,cAAc,gDAAgD,IAC9D;4BACF,UAAU;;;;;;sCAIZ,6LAAC;4BAAI,WAAW,CAAC,kCAAkC,EACjD,cAAc,qBAAqB,yBACnC;;gCACC;gCAAU;gCAAE;;;;;;;;;;;;;;;;;;0BAMnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAK,WAAU;;oCAA4C;oCACxC,WAAW,MAAM;oCAAC;;;;;;;;;;;;;kCAGxC,6LAAC,4JAAA,CAAA,UAAe;wBACd,oBAAoB;wBACpB,kBAAkB;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAIhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO,cAAc,CAAC,YAAY,OAAO;wBAAE;wBACzD,UAAU;4BAAE,OAAO,cAAc,CAAC,YAAY,OAAO;wBAAE;kCAEvD,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAS;4BACT,UAAU,CAAC,cAAc,eAAe;4BACxC,WAAU;4BACV,OAAO;gCACL,YAAY,cAAc,CAAC,YACvB,sDACA;gCACJ,WAAW,cAAc,CAAC,YACtB,uCACA;4BACN;sCAEA,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACnB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC1B,WAAU;;sDAEV,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;mCAN7C;;;;yDAUN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC1B,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;mCAN9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBtB;GA1MwB;KAAA", "debugId": null}}, {"offset": {"line": 3436, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/posts/crud.ts"], "sourcesContent": ["'use server';\r\n\r\nimport { createClient } from '@/utils/supabase/server';\r\nimport { revalidatePath } from 'next/cache';\r\nimport { PostFormData } from '@/lib/types/posts';\r\nimport { ActionResponse } from '@/lib/types/api';\r\nimport { deletePostMedia } from '@/lib/actions/shared/upload-post-media';\r\n\r\n/**\r\n * Create a new post\r\n */\r\nexport async function createPost(formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's business profile\r\n  const { data: businessProfile, error: profileError } = await supabase\r\n    .from('business_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !businessProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Business profile not found',\r\n      error: 'You must have a business profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    business_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: businessProfile.city_slug,\r\n    state_slug: businessProfile.state_slug,\r\n    locality_slug: businessProfile.locality_slug,\r\n    pincode: businessProfile.pincode,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || []\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the content of an existing post (for inline editing)\r\n */\r\nexport async function updatePostContent(postId: string, content: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the content and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      content: content.trim(),\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post content:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update only the product_ids of an existing post (for inline editing)\r\n */\r\nexport async function updatePostProducts(postId: string, productIds: string[]): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Update only the product_ids and timestamp\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update({\r\n      product_ids: productIds,\r\n      updated_at: new Date().toISOString()\r\n    })\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post products:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post products',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post products updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing post (full update for form submissions)\r\n */\r\nexport async function updatePost(postId: string, formData: PostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    product_ids: formData.product_ids || [],\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('business_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a post\r\n */\r\nexport async function deletePost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user, get creation date for media deletion\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('business_posts')\r\n    .select('id, created_at, image_url')\r\n    .eq('id', postId)\r\n    .eq('business_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // Always attempt to delete the post folder from storage\r\n  // This ensures we clean up any files that might exist, regardless of image_url status\r\n  try {\r\n    const mediaDeleteResult = await deletePostMedia(user.id, postId, existingPost.created_at);\r\n    if (!mediaDeleteResult.success && mediaDeleteResult.error) {\r\n      console.error('Error deleting post media:', mediaDeleteResult.error);\r\n      // Continue with post deletion even if media deletion fails\r\n    }\r\n  } catch (mediaError) {\r\n    console.error('Error deleting post media:', mediaError);\r\n    // Continue with post deletion even if media deletion fails\r\n  }\r\n\r\n  // Delete the post\r\n  const { error } = await supabase\r\n    .from('business_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  // Revalidate the feed pages\r\n  revalidatePath('/dashboard/business/feed');\r\n  revalidatePath('/dashboard/customer/feed');\r\n  revalidatePath('/dashboard/business/posts');\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4RsB,aAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/dialogs/PostDeleteDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogDescription,\r\n  <PERSON><PERSON><PERSON><PERSON>er,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Loader2, Trash2 } from \"lucide-react\";\r\nimport { toast } from \"sonner\";\r\nimport { deletePost } from \"@/lib/actions/posts\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nexport interface PostDeleteDialogProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (_open: boolean) => void;\r\n  postId: string;\r\n  postContent: string;\r\n  onDeleteSuccess?: () => void;\r\n}\r\n\r\nexport default function PostDeleteDialog({\r\n  isOpen,\r\n  onOpenChange,\r\n  postId,\r\n  postContent: _postContent,\r\n  onDeleteSuccess,\r\n}: PostDeleteDialogProps) {\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  const handleDelete = async () => {\r\n    setIsDeleting(true);\r\n\r\n    try {\r\n      const result = await deletePost(postId);\r\n\r\n      if (result.success) {\r\n        toast.success(\"Post deleted successfully\", {\r\n          description: \"Your post and associated media have been removed.\"\r\n        });\r\n        onDeleteSuccess?.();\r\n        onOpenChange(false);\r\n      } else {\r\n        toast.error(\"Failed to delete post\", {\r\n          description: result.error || \"Please try again.\"\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting post:\", error);\r\n      toast.error(\"An unexpected error occurred\", {\r\n        description: \"Please try again later.\"\r\n      });\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader className=\"text-center pb-2\">\r\n          <div className=\"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-50 dark:bg-red-950/20\">\r\n            <motion.div\r\n              initial={{ scale: 0 }}\r\n              animate={{ scale: 1 }}\r\n              transition={{ delay: 0.1, type: \"spring\", stiffness: 200 }}\r\n            >\r\n              <Trash2 className=\"h-8 w-8 text-red-500\" />\r\n            </motion.div>\r\n          </div>\r\n          <DialogTitle className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n            Delete this post?\r\n          </DialogTitle>\r\n          <DialogDescription className=\"text-gray-500 dark:text-gray-400 mt-2\">\r\n            This action cannot be undone.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <DialogFooter className=\"flex flex-col-reverse sm:flex-row gap-3 pt-4\">\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={() => onOpenChange(false)}\r\n            disabled={isDeleting}\r\n            className=\"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n\r\n          <motion.div\r\n            whileHover={{ scale: 1.02 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            className=\"flex-1\"\r\n          >\r\n            <Button\r\n              type=\"button\"\r\n              onClick={handleDelete}\r\n              disabled={isDeleting}\r\n              className={`\r\n                w-full relative overflow-hidden\r\n                bg-gradient-to-r from-red-500 to-red-600\r\n                hover:from-red-600 hover:to-red-700\r\n                text-white font-medium\r\n                shadow-lg hover:shadow-xl\r\n                transition-all duration-300\r\n                before:absolute before:inset-0\r\n                before:bg-gradient-to-r before:from-red-400 before:to-red-500\r\n                before:opacity-0 hover:before:opacity-20\r\n                before:transition-opacity before:duration-300\r\n                ${isDeleting ? 'cursor-not-allowed opacity-80' : ''}\r\n              `}\r\n              style={{\r\n                boxShadow: isDeleting\r\n                  ? '0 4px 20px rgba(239, 68, 68, 0.3)'\r\n                  : '0 4px 20px rgba(239, 68, 68, 0.4), 0 0 20px rgba(239, 68, 68, 0.2)'\r\n              }}\r\n            >\r\n              <AnimatePresence mode=\"wait\">\r\n                {isDeleting ? (\r\n                  <motion.div\r\n                    key=\"deleting\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 10 }}\r\n                    className=\"flex items-center justify-center\"\r\n                  >\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    Deleting...\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    key=\"delete\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 10 }}\r\n                    className=\"flex items-center justify-center\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                    Delete Post\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n            </Button>\r\n          </motion.div>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\n// Trigger button component for easier usage\r\nexport interface PostDeleteButtonProps {\r\n  postId: string;\r\n  postContent: string;\r\n  onDeleteSuccess?: () => void;\r\n  variant?: \"default\" | \"destructive\" | \"outline\" | \"secondary\" | \"ghost\" | \"link\";\r\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\";\r\n  className?: string;\r\n}\r\n\r\nexport function PostDeleteButton({\r\n  postId,\r\n  postContent,\r\n  onDeleteSuccess,\r\n  variant = \"ghost\",\r\n  size = \"sm\",\r\n  className = \"\",\r\n}: PostDeleteButtonProps) {\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n\r\n  return (\r\n    <>\r\n      <Button\r\n        variant={variant}\r\n        size={size}\r\n        onClick={() => setIsDialogOpen(true)}\r\n        className={className}\r\n      >\r\n        <Trash2 className=\"h-4 w-4\" />\r\n      </Button>\r\n\r\n      <PostDeleteDialog\r\n        isOpen={isDialogOpen}\r\n        onOpenChange={setIsDialogOpen}\r\n        postId={postId}\r\n        postContent={postContent}\r\n        onDeleteSuccess={onDeleteSuccess}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AACA;AAAA;AACA;AACA;AAAA;AACA;AAAA;;;AAfA;;;;;;;;AAyBe,SAAS,iBAAiB,EACvC,MAAM,EACN,YAAY,EACZ,MAAM,EACN,aAAa,YAAY,EACzB,eAAe,EACO;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,cAAc;QAEd,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,kKAAA,CAAA,aAAU,AAAD,EAAE;YAEhC,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;oBACzC,aAAa;gBACf;gBACA;gBACA,aAAa;YACf,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;gBAC1C,aAAa;YACf;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;0CAEzD,cAAA,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAGtB,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;sCAAyD;;;;;;sCAGhF,6LAAC,8HAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAwC;;;;;;;;;;;;8BAKvE,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAU;sCACX;;;;;;sCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAW,CAAC;;;;;;;;;;;gBAWV,EAAE,aAAa,kCAAkC,GAAG;cACtD,CAAC;gCACD,OAAO;oCACL,WAAW,aACP,sCACA;gCACN;0CAEA,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,MAAK;8CACnB,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,WAAU;;0DAEV,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uCAN7C;;;;6DAUN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;uCAN/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBxB;GA/HwB;KAAA;AA2IjB,SAAS,iBAAiB,EAC/B,MAAM,EACN,WAAW,EACX,eAAe,EACf,UAAU,OAAO,EACjB,OAAO,IAAI,EACX,YAAY,EAAE,EACQ;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE;;0BACE,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAS;gBACT,MAAM;gBACN,SAAS,IAAM,gBAAgB;gBAC/B,WAAW;0BAEX,cAAA,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,6LAAC;gBACC,QAAQ;gBACR,cAAc;gBACd,QAAQ;gBACR,aAAa;gBACb,iBAAiB;;;;;;;;AAIzB;IA9BgB;MAAA", "debugId": null}}, {"offset": {"line": 3753, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/postUrl.ts"], "sourcesContent": ["/**\r\n * Utility functions for post URL generation and handling\r\n */\r\n\r\n/**\r\n * Generate a complete URL for a single post page\r\n * @param postId - The ID of the post\r\n * @returns Complete URL for the post\r\n */\r\nexport function generatePostUrl(postId: string): string {\r\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in';\r\n  return `${baseUrl}/post/${postId}`;\r\n}\r\n\r\n/**\r\n * Generate a relative path for a single post page\r\n * @param postId - The ID of the post\r\n * @returns Relative path for the post\r\n */\r\nexport function generatePostPath(postId: string): string {\r\n  return `/post/${postId}`;\r\n}\r\n\r\n/**\r\n * Extract post ID from a post URL\r\n * @param url - The post URL\r\n * @returns Post ID if valid, null otherwise\r\n */\r\nexport function extractPostIdFromUrl(url: string): string | null {\r\n  try {\r\n    const urlObj = new URL(url);\r\n    const pathParts = urlObj.pathname.split('/');\r\n    \r\n    // Expected format: /post/[postId]\r\n    if (pathParts.length >= 3 && pathParts[1] === 'post') {\r\n      const postId = pathParts[2];\r\n      return postId && postId.trim() !== '' ? postId : null;\r\n    }\r\n    \r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error extracting post ID from URL:', error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Validate if a string is a valid post ID format\r\n * @param postId - The post ID to validate\r\n * @returns True if valid, false otherwise\r\n */\r\nexport function isValidPostId(postId: string): boolean {\r\n  if (!postId || typeof postId !== 'string') {\r\n    return false;\r\n  }\r\n  \r\n  // Basic validation - should be a non-empty string\r\n  // You can add more specific validation based on your post ID format\r\n  return postId.trim().length > 0;\r\n}\r\n\r\n/**\r\n * Generate sharing text for social media\r\n * @param postId - The ID of the post\r\n * @param authorName - Optional author name\r\n * @returns Formatted sharing text\r\n */\r\nexport function generateSharingText(postId: string, authorName?: string): string {\r\n  const postUrl = generatePostUrl(postId);\r\n  \r\n  if (authorName) {\r\n    return `Check out this post by ${authorName} on Dukancard: ${postUrl}`;\r\n  }\r\n  \r\n  return `Check out this post on Dukancard: ${postUrl}`;\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;;;;;AAEiB;AADX,SAAS,gBAAgB,MAAc;IAC5C,MAAM,UAAU,6DAAoC;IACpD,OAAO,GAAG,QAAQ,MAAM,EAAE,QAAQ;AACpC;AAOO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,CAAC,MAAM,EAAE,QAAQ;AAC1B;AAOO,SAAS,qBAAqB,GAAW;IAC9C,IAAI;QACF,MAAM,SAAS,IAAI,IAAI;QACvB,MAAM,YAAY,OAAO,QAAQ,CAAC,KAAK,CAAC;QAExC,kCAAkC;QAClC,IAAI,UAAU,MAAM,IAAI,KAAK,SAAS,CAAC,EAAE,KAAK,QAAQ;YACpD,MAAM,SAAS,SAAS,CAAC,EAAE;YAC3B,OAAO,UAAU,OAAO,IAAI,OAAO,KAAK,SAAS;QACnD;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAOO,SAAS,cAAc,MAAc;IAC1C,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,OAAO;IACT;IAEA,kDAAkD;IAClD,oEAAoE;IACpE,OAAO,OAAO,IAAI,GAAG,MAAM,GAAG;AAChC;AAQO,SAAS,oBAAoB,MAAc,EAAE,UAAmB;IACrE,MAAM,UAAU,gBAAgB;IAEhC,IAAI,YAAY;QACd,OAAO,CAAC,uBAAuB,EAAE,WAAW,eAAe,EAAE,SAAS;IACxE;IAEA,OAAO,CAAC,kCAAkC,EAAE,SAAS;AACvD", "debugId": null}}, {"offset": {"line": 3813, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernPostCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { motion } from 'framer-motion';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { PostWithBusinessProfile, ProductData } from '@/lib/types/posts';\r\nimport PostActions from './PostActions';\r\nimport ProductListItem from '@/app/components/ProductListItem';\r\nimport { fetchProductsByIds } from '@/lib/actions/products/fetchProductsByIds';\r\nimport { Loader2 } from 'lucide-react';\r\nimport { MapPin, MoreVertical, Edit3, Trash2, Package, Share2, User } from 'lucide-react';\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { cn } from '@/lib/utils';\r\nimport { fetchPostAddress, formatAddressString, formatAddressFromSlugs, PostAddress } from '@/lib/utils/addressUtils';\r\nimport { usePostOwnership } from '@/components/feed/shared/hooks/usePostOwnership';\r\nimport InlinePostAndProductEditor from '@/components/feed/shared/editors/InlinePostAndProductEditor';\r\nimport PostDeleteDialog from '@/components/feed/shared/dialogs/PostDeleteDialog';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Button } from '@/components/ui/button';\r\nimport { generatePostPath, generatePostUrl } from '@/lib/utils/postUrl';\r\nimport { toast } from 'sonner';\r\n\r\ninterface ModernPostCardProps {\r\n  post: PostWithBusinessProfile;\r\n  index?: number;\r\n  onPostUpdate?: (_postId: string, _newContent: string) => void;\r\n  onPostDelete?: (_postId: string) => void;\r\n  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;\r\n  showActualAspectRatio?: boolean; // For single post pages\r\n  disablePostClick?: boolean; // Disable post clicking for single post pages\r\n  enableImageFullscreen?: boolean; // Enable full-screen image viewing\r\n}\r\n\r\nexport default function ModernPostCard({\r\n  post,\r\n  index = 0,\r\n  onPostUpdate,\r\n  onPostDelete,\r\n  onProductsUpdate,\r\n  showActualAspectRatio = false,\r\n  disablePostClick = false,\r\n  enableImageFullscreen = false\r\n}: ModernPostCardProps) {\r\n  const [imageLoading, setImageLoading] = useState(true);\r\n  const [imageError, setImageError] = useState(false);\r\n  const [address, setAddress] = useState<PostAddress | null>(null);\r\n  const [addressLoading, setAddressLoading] = useState(true);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n  const [currentContent, setCurrentContent] = useState(post.content);\r\n  const [currentProductIds, setCurrentProductIds] = useState<string[]>(post.product_ids || []);\r\n  const [linkedProducts, setLinkedProducts] = useState<ProductData[]>([]);\r\n  const [isLoadingProducts, setIsLoadingProducts] = useState(false);\r\n  const [showFullscreenImage, setShowFullscreenImage] = useState(false);\r\n  const productsScrollRef = useRef<HTMLDivElement>(null);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [startX, setStartX] = useState(0);\r\n  const [scrollLeft, setScrollLeft] = useState(0);\r\n  const [dragDistance, setDragDistance] = useState(0);\r\n\r\n  // Check if current user owns this post\r\n  const { isOwner, isLoading: ownershipLoading } = usePostOwnership({\r\n    postBusinessId: post.business_id\r\n  });\r\n\r\n  // Get business profile data\r\n  const business = post.business_profiles;\r\n\r\n  // Handle edit save (both content and products)\r\n  const handleEditSave = (newContent: string, newProductIds: string[]) => {\r\n    setIsEditing(false);\r\n    setCurrentContent(newContent);\r\n    setCurrentProductIds(newProductIds);\r\n    onPostUpdate?.(post.id, newContent);\r\n    onProductsUpdate?.(post.id, newProductIds);\r\n  };\r\n\r\n  // Handle edit cancel\r\n  const handleEditCancel = () => {\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle delete success\r\n  const handleDeleteSuccess = () => {\r\n    setShowDeleteDialog(false);\r\n    onPostDelete?.(post.id);\r\n  };\r\n\r\n  const handleShare = async () => {\r\n    try {\r\n      const postUrl = generatePostUrl(post.id);\r\n\r\n      // Try to use native Web Share API if available\r\n      if (navigator.share) {\r\n        await navigator.share({\r\n          title: 'Check out this post on Dukancard',\r\n          url: postUrl,\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Fallback to clipboard API\r\n      await navigator.clipboard.writeText(postUrl);\r\n      toast.success('Post link copied to clipboard!');\r\n\r\n    } catch (error) {\r\n      console.error('Error sharing post:', error);\r\n      toast.error('Failed to share post');\r\n    }\r\n  };\r\n\r\n  // Fetch linked products\r\n  useEffect(() => {\r\n    if (currentProductIds.length === 0) {\r\n      setLinkedProducts([]);\r\n      setIsLoadingProducts(false);\r\n      return;\r\n    }\r\n\r\n    const fetchProducts = async () => {\r\n      setIsLoadingProducts(true);\r\n      try {\r\n        const result = await fetchProductsByIds(currentProductIds);\r\n\r\n        if (!result.success) {\r\n          console.error('Error fetching products:', result.error);\r\n          setLinkedProducts([]);\r\n          return;\r\n        }\r\n\r\n        // Maintain the order of products as specified in currentProductIds array\r\n        const orderedProducts = currentProductIds\r\n          .map(id => result.data?.find(product => product.id === id))\r\n          .filter(Boolean) as ProductData[];\r\n\r\n        setLinkedProducts(orderedProducts);\r\n      } catch (err) {\r\n        console.error('Error fetching products:', err);\r\n        setLinkedProducts([]);\r\n      } finally {\r\n        setIsLoadingProducts(false);\r\n      }\r\n    };\r\n\r\n    fetchProducts();\r\n  }, [currentProductIds]);\r\n\r\n  // Add drag-to-scroll functionality for products container\r\n  useEffect(() => {\r\n    const container = productsScrollRef.current;\r\n    if (!container) return;\r\n\r\n    const handleMouseDown = (e: MouseEvent) => {\r\n      if (container.scrollWidth <= container.clientWidth) return; // No overflow, no need to scroll\r\n\r\n      setIsDragging(true);\r\n      setStartX(e.pageX - container.offsetLeft);\r\n      setScrollLeft(container.scrollLeft);\r\n      setDragDistance(0);\r\n      container.style.cursor = 'grabbing';\r\n      e.preventDefault();\r\n    };\r\n\r\n    const handleMouseMove = (e: MouseEvent) => {\r\n      if (!isDragging || container.scrollWidth <= container.clientWidth) return;\r\n\r\n      e.preventDefault();\r\n      const x = e.pageX - container.offsetLeft;\r\n      const walk = (x - startX) * 2; // Scroll speed multiplier\r\n      const newDragDistance = Math.abs(x - startX);\r\n      setDragDistance(newDragDistance);\r\n      container.scrollLeft = scrollLeft - walk;\r\n    };\r\n\r\n    const handleMouseUp = () => {\r\n      setIsDragging(false);\r\n      container.style.cursor = container.scrollWidth > container.clientWidth ? 'grab' : 'default';\r\n    };\r\n\r\n    const handleMouseLeave = () => {\r\n      setIsDragging(false);\r\n      container.style.cursor = container.scrollWidth > container.clientWidth ? 'grab' : 'default';\r\n    };\r\n\r\n    // Set initial cursor\r\n    container.style.cursor = container.scrollWidth > container.clientWidth ? 'grab' : 'default';\r\n\r\n    container.addEventListener('mousedown', handleMouseDown);\r\n    document.addEventListener('mousemove', handleMouseMove);\r\n    document.addEventListener('mouseup', handleMouseUp);\r\n    container.addEventListener('mouseleave', handleMouseLeave);\r\n\r\n    return () => {\r\n      container.removeEventListener('mousedown', handleMouseDown);\r\n      document.removeEventListener('mousemove', handleMouseMove);\r\n      document.removeEventListener('mouseup', handleMouseUp);\r\n      container.removeEventListener('mouseleave', handleMouseLeave);\r\n    };\r\n  }, [linkedProducts, isDragging, startX, scrollLeft, dragDistance]);\r\n\r\n  // Handle product click - only navigate if it wasn't a drag\r\n  const handleProductClick = (e: React.MouseEvent, productUrl: string) => {\r\n    if (dragDistance > 5) { // If dragged more than 5px, don't navigate\r\n      e.preventDefault();\r\n      return;\r\n    }\r\n    // Allow normal navigation for clicks\r\n    window.open(productUrl, '_blank', 'noopener,noreferrer');\r\n  };\r\n\r\n  // Fetch real address data - moved before conditional return\r\n  useEffect(() => {\r\n    if (!business) return; // Early return if no business data\r\n\r\n    const loadAddress = async () => {\r\n      setAddressLoading(true);\r\n      try {\r\n        const addressData = await fetchPostAddress(\r\n          post.locality_slug,\r\n          post.city_slug,\r\n          post.state_slug,\r\n          post.pincode\r\n        );\r\n        setAddress(addressData);\r\n      } catch (error) {\r\n        console.error('Error fetching address:', error);\r\n        setAddress(null);\r\n      } finally {\r\n        setAddressLoading(false);\r\n      }\r\n    };\r\n\r\n    loadAddress();\r\n  }, [business, post.locality_slug, post.city_slug, post.state_slug, post.pincode]);\r\n\r\n  if (!business) {\r\n    return null; // Skip rendering if business profile is missing\r\n  }\r\n\r\n  // Format the post date\r\n  const formattedDate = formatDistanceToNow(new Date(post.created_at), { addSuffix: true });\r\n\r\n  // Avatar fallback now uses User icon instead of initials\r\n\r\n  // Format address display\r\n  const getDisplayAddress = () => {\r\n    if (addressLoading) {\r\n      return 'Loading address...';\r\n    }\r\n\r\n    if (address) {\r\n      return formatAddressString(address);\r\n    }\r\n\r\n    // Fallback to slug-based formatting\r\n    return formatAddressFromSlugs(\r\n      post.locality_slug,\r\n      post.city_slug,\r\n      post.state_slug,\r\n      post.pincode\r\n    );\r\n  };\r\n\r\n  // Animation variants\r\n  const cardVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.4,\r\n        delay: index * 0.1,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={cardVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      whileHover={{\r\n        y: -2,\r\n        transition: { duration: 0.2, ease: \"easeOut\" }\r\n      }}\r\n      className={cn(\r\n        \"bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800\",\r\n        \"shadow-sm hover:shadow-md transition-all duration-300\",\r\n        \"overflow-hidden mb-4 md:mb-6\"\r\n      )}\r\n    >\r\n      {/* Post Header */}\r\n      <div className=\"p-4 pb-2\">\r\n        {/* Business Info and Time */}\r\n        <div className=\"flex items-center justify-between mb-3\">\r\n          {business.business_slug ? (\r\n            <Link\r\n              href={`/${business.business_slug}`}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"flex items-center space-x-3 flex-1 min-w-0 group\"\r\n            >\r\n              <Avatar className=\"h-12 w-12 border-2 border-[var(--brand-gold)]/30 transition-transform group-hover:scale-105\">\r\n                <AvatarImage\r\n                  src={business.logo_url || ''}\r\n                  alt={business.business_name || 'Business'}\r\n                  className=\"object-cover\"\r\n                />\r\n                <AvatarFallback className=\"bg-muted text-foreground border border-[var(--brand-gold)]/30\">\r\n                  <User className=\"h-6 w-6\" />\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h3 className=\"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate group-hover:text-[var(--brand-gold)] transition-colors\">\r\n                  {business.business_name}\r\n                </h3>\r\n                {business.business_slug && (\r\n                  <div className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n                    @{business.business_slug}\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-neutral-400 dark:text-neutral-500 mt-1\">\r\n                  {formattedDate}\r\n                </div>\r\n              </div>\r\n            </Link>\r\n          ) : (\r\n            <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\r\n              <Avatar className=\"h-12 w-12 border-2 border-[var(--brand-gold)]/30\">\r\n                <AvatarImage\r\n                  src={business.logo_url || ''}\r\n                  alt={business.business_name || 'Customer'}\r\n                  className=\"object-cover\"\r\n                />\r\n                <AvatarFallback className=\"bg-muted text-foreground border border-[var(--brand-gold)]/30\">\r\n                  <User className=\"h-6 w-6\" />\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <h3 className=\"font-semibold text-base text-neutral-900 dark:text-neutral-100 truncate\">\r\n                  {business.business_name}\r\n                </h3>\r\n                <div className=\"text-xs text-neutral-400 dark:text-neutral-500 mt-1\">\r\n                  {formattedDate}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          {/* Three-dot menu - show for all posts */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full\"\r\n              >\r\n                <MoreVertical className=\"h-5 w-5 text-neutral-500 dark:text-neutral-400\" />\r\n                <span className=\"sr-only\">Open post menu</span>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-48\">\r\n              {/* Share option - available for all posts */}\r\n              <DropdownMenuItem\r\n                onClick={handleShare}\r\n                className=\"cursor-pointer\"\r\n              >\r\n                <Share2 className=\"h-4 w-4 mr-2\" />\r\n                Share post\r\n              </DropdownMenuItem>\r\n\r\n              {/* Owner-only options */}\r\n              {isOwner && !ownershipLoading && (\r\n                <>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem\r\n                    onClick={() => setIsEditing(true)}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    <Edit3 className=\"h-4 w-4 mr-2\" />\r\n                    Edit post\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuSeparator />\r\n                  <DropdownMenuItem\r\n                    onClick={() => setShowDeleteDialog(true)}\r\n                    className=\"text-destructive focus:text-destructive cursor-pointer\"\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                    Delete post\r\n                  </DropdownMenuItem>\r\n                </>\r\n              )}\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n\r\n        {/* Address */}\r\n        <div className=\"flex items-center text-sm text-neutral-500 dark:text-neutral-400\">\r\n          <MapPin className=\"h-3.5 w-3.5 mr-1 flex-shrink-0\" />\r\n          <span>{getDisplayAddress()}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Post Content */}\r\n      <div className=\"px-4 pb-3\">\r\n        {isEditing ? (\r\n          <InlinePostAndProductEditor\r\n            postId={post.id}\r\n            initialContent={currentContent}\r\n            initialProductIds={currentProductIds}\r\n            initialImageUrl={post.image_url}\r\n            onSave={handleEditSave}\r\n            onCancel={handleEditCancel}\r\n          />\r\n        ) : disablePostClick ? (\r\n          <p className=\"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line\">\r\n            {currentContent}\r\n          </p>\r\n        ) : (\r\n          <Link\r\n            href={generatePostPath(post.id)}\r\n            className=\"block cursor-pointer\"\r\n          >\r\n            <p className=\"text-neutral-900 dark:text-neutral-100 text-sm leading-relaxed whitespace-pre-line\">\r\n              {currentContent}\r\n            </p>\r\n          </Link>\r\n        )}\r\n      </div>\r\n\r\n      {/* Post Image */}\r\n      {post.image_url && (\r\n        disablePostClick ? (\r\n          <div\r\n            className={cn(\r\n              \"relative w-full transition-opacity duration-200\",\r\n              enableImageFullscreen ? \"cursor-pointer hover:opacity-95\" : \"\"\r\n            )}\r\n            onClick={enableImageFullscreen ? () => setShowFullscreenImage(true) : undefined}\r\n          >\r\n            {showActualAspectRatio ? (\r\n              <div className=\"relative w-full bg-neutral-100 dark:bg-neutral-800\">\r\n                <Image\r\n                  src={post.image_url}\r\n                  alt=\"Post image\"\r\n                  width={800}\r\n                  height={600}\r\n                  className={cn(\r\n                    \"w-full h-auto object-contain transition-all duration-300\",\r\n                    imageLoading && \"blur-sm scale-105\",\r\n                    imageError && \"hidden\"\r\n                  )}\r\n                  onLoad={() => setImageLoading(false)}\r\n                  onError={() => {\r\n                    setImageError(true);\r\n                    setImageLoading(false);\r\n                  }}\r\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                  priority={index < 3}\r\n                />\r\n                {imageLoading && (\r\n                  <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <div className=\"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800\">\r\n                <Image\r\n                  src={post.image_url}\r\n                  alt=\"Post image\"\r\n                  fill\r\n                  className={cn(\r\n                    \"object-cover transition-all duration-300\",\r\n                    imageLoading && \"blur-sm scale-105\",\r\n                    imageError && \"hidden\"\r\n                  )}\r\n                  onLoad={() => setImageLoading(false)}\r\n                  onError={() => {\r\n                    setImageError(true);\r\n                    setImageLoading(false);\r\n                  }}\r\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                  priority={index < 3}\r\n                />\r\n                {imageLoading && (\r\n                  <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <Link href={generatePostPath(post.id)} className=\"block\">\r\n            <div className=\"relative w-full cursor-pointer hover:opacity-95 transition-opacity duration-200\">\r\n              {showActualAspectRatio ? (\r\n                <div className=\"relative w-full bg-neutral-100 dark:bg-neutral-800\">\r\n                  <Image\r\n                    src={post.image_url}\r\n                    alt=\"Post image\"\r\n                    width={800}\r\n                    height={600}\r\n                    className={cn(\r\n                      \"w-full h-auto object-contain transition-all duration-300\",\r\n                      imageLoading && \"blur-sm scale-105\",\r\n                      imageError && \"hidden\"\r\n                    )}\r\n                    onLoad={() => setImageLoading(false)}\r\n                    onError={() => {\r\n                      setImageError(true);\r\n                      setImageLoading(false);\r\n                    }}\r\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                    priority={index < 3}\r\n                  />\r\n                  {imageLoading && (\r\n                    <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"relative w-full aspect-[4/3] bg-neutral-100 dark:bg-neutral-800\">\r\n                  <Image\r\n                    src={post.image_url}\r\n                    alt=\"Post image\"\r\n                    fill\r\n                    className={cn(\r\n                      \"object-cover transition-all duration-300\",\r\n                      imageLoading && \"blur-sm scale-105\",\r\n                      imageError && \"hidden\"\r\n                    )}\r\n                    onLoad={() => setImageLoading(false)}\r\n                    onError={() => {\r\n                      setImageError(true);\r\n                      setImageLoading(false);\r\n                    }}\r\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n                    priority={index < 3}\r\n                  />\r\n                  {imageLoading && (\r\n                    <div className=\"absolute inset-0 bg-neutral-200 dark:bg-neutral-700 animate-pulse\" />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </Link>\r\n        )\r\n      )}\r\n\r\n      {/* Linked Products */}\r\n      {currentProductIds.length > 0 && !isEditing && (\r\n        <div className=\"px-4 pt-4\">\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-1.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-lg\">\r\n                <Package className=\"h-4 w-4 text-[var(--brand-gold)]\" />\r\n              </div>\r\n              <h4 className=\"text-sm font-semibold text-neutral-900 dark:text-neutral-100\">\r\n                Featured Products ({linkedProducts.length})\r\n              </h4>\r\n            </div>\r\n\r\n              {isLoadingProducts ? (\r\n                <div className=\"flex justify-center py-8\">\r\n                  <div className=\"flex flex-col items-center gap-2\">\r\n                    <Loader2 className=\"h-6 w-6 animate-spin text-[var(--brand-gold)]\" />\r\n                    <span className=\"text-xs text-muted-foreground\">Loading products...</span>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <motion.div\r\n                  ref={productsScrollRef}\r\n                  className=\"flex gap-3 overflow-x-auto scrollbar-hide pb-2\"\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.3 }}\r\n                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}\r\n                >\r\n                  {linkedProducts.map((product) => (\r\n                    <motion.div\r\n                      key={product.id}\r\n                      className=\"flex-shrink-0 w-40\"\r\n                      initial={{ opacity: 0, scale: 0.9 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      transition={{ duration: 0.2 }}\r\n                    >\r\n                      <div\r\n                        className=\"block h-full cursor-pointer\"\r\n                        onClick={(e) => {\r\n                          if (business.business_slug) {\r\n                            handleProductClick(e, `/${business.business_slug}/product/${product.slug || product.id}`);\r\n                          }\r\n                        }}\r\n                      >\r\n                        <ProductListItem\r\n                          product={{\r\n                            ...product,\r\n                            // Convert to ProductServiceData format\r\n                            description: undefined,\r\n                            product_type: 'physical' as const,\r\n                            base_price: product.base_price || 0,\r\n                            slug: product.slug || undefined,\r\n                            is_available: true,\r\n                            images: product.image_url ? [product.image_url] : undefined,\r\n                            featured_image_index: 0,\r\n                            created_at: new Date(),\r\n                            updated_at: new Date(),\r\n                          }}\r\n                          isLink={false}\r\n                        />\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Post Actions */}\r\n      <div className=\"p-4 pt-3\">\r\n        <PostActions\r\n          business={business}\r\n          hasWhatsApp={!!(business.whatsapp_number && business.whatsapp_number.trim() !== '')}\r\n          hasPhone={!!(business.phone && business.phone.trim() !== '')}\r\n        />\r\n      </div>\r\n\r\n      {/* Delete Dialog */}\r\n      <PostDeleteDialog\r\n        isOpen={showDeleteDialog}\r\n        onOpenChange={setShowDeleteDialog}\r\n        postId={post.id}\r\n        postContent={currentContent}\r\n        onDeleteSuccess={handleDeleteSuccess}\r\n      />\r\n\r\n      {/* Full-screen Image Modal */}\r\n      {enableImageFullscreen && showFullscreenImage && post.image_url && (\r\n        <div\r\n          className=\"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4\"\r\n          onClick={() => setShowFullscreenImage(false)}\r\n        >\r\n          <div className=\"relative max-w-full max-h-full\">\r\n            <button\r\n              onClick={() => setShowFullscreenImage(false)}\r\n              className=\"absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors\"\r\n            >\r\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n            </button>\r\n            <Image\r\n              src={post.image_url}\r\n              alt=\"Post image fullscreen\"\r\n              width={1200}\r\n              height={800}\r\n              className=\"max-w-full max-h-full object-contain\"\r\n              onClick={(e) => e.stopPropagation()}\r\n            />\r\n          </div>\r\n        </div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;;AA5BA;;;;;;;;;;;;;;;;;;;;;AAyCe,SAAS,eAAe,EACrC,IAAI,EACJ,QAAQ,CAAC,EACT,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,wBAAwB,KAAK,EAC7B,mBAAmB,KAAK,EACxB,wBAAwB,KAAK,EACT;;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,OAAO;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,KAAK,WAAW,IAAI,EAAE;IAC3F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uCAAuC;IACvC,MAAM,EAAE,OAAO,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,mBAAgB,AAAD,EAAE;QAChE,gBAAgB,KAAK,WAAW;IAClC;IAEA,4BAA4B;IAC5B,MAAM,WAAW,KAAK,iBAAiB;IAEvC,+CAA+C;IAC/C,MAAM,iBAAiB,CAAC,YAAoB;QAC1C,aAAa;QACb,kBAAkB;QAClB,qBAAqB;QACrB,eAAe,KAAK,EAAE,EAAE;QACxB,mBAAmB,KAAK,EAAE,EAAE;IAC9B;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,aAAa;IACf;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,oBAAoB;QACpB,eAAe,KAAK,EAAE;IACxB;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,EAAE;YAEvC,+CAA+C;YAC/C,IAAI,UAAU,KAAK,EAAE;gBACnB,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO;oBACP,KAAK;gBACP;gBACA;YACF;YAEA,4BAA4B;YAC5B,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAEhB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB,MAAM,KAAK,GAAG;gBAClC,kBAAkB,EAAE;gBACpB,qBAAqB;gBACrB;YACF;YAEA,MAAM;0DAAgB;oBACpB,qBAAqB;oBACrB,IAAI;wBACF,MAAM,SAAS,MAAM,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD,EAAE;wBAExC,IAAI,CAAC,OAAO,OAAO,EAAE;4BACnB,QAAQ,KAAK,CAAC,4BAA4B,OAAO,KAAK;4BACtD,kBAAkB,EAAE;4BACpB;wBACF;wBAEA,yEAAyE;wBACzE,MAAM,kBAAkB,kBACrB,GAAG;sFAAC,CAAA,KAAM,OAAO,IAAI,EAAE;8FAAK,CAAA,UAAW,QAAQ,EAAE,KAAK;;qFACtD,MAAM,CAAC;wBAEV,kBAAkB;oBACpB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,kBAAkB,EAAE;oBACtB,SAAU;wBACR,qBAAqB;oBACvB;gBACF;;YAEA;QACF;mCAAG;QAAC;KAAkB;IAEtB,0DAA0D;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,YAAY,kBAAkB,OAAO;YAC3C,IAAI,CAAC,WAAW;YAEhB,MAAM;4DAAkB,CAAC;oBACvB,IAAI,UAAU,WAAW,IAAI,UAAU,WAAW,EAAE,QAAQ,iCAAiC;oBAE7F,cAAc;oBACd,UAAU,EAAE,KAAK,GAAG,UAAU,UAAU;oBACxC,cAAc,UAAU,UAAU;oBAClC,gBAAgB;oBAChB,UAAU,KAAK,CAAC,MAAM,GAAG;oBACzB,EAAE,cAAc;gBAClB;;YAEA,MAAM;4DAAkB,CAAC;oBACvB,IAAI,CAAC,cAAc,UAAU,WAAW,IAAI,UAAU,WAAW,EAAE;oBAEnE,EAAE,cAAc;oBAChB,MAAM,IAAI,EAAE,KAAK,GAAG,UAAU,UAAU;oBACxC,MAAM,OAAO,CAAC,IAAI,MAAM,IAAI,GAAG,0BAA0B;oBACzD,MAAM,kBAAkB,KAAK,GAAG,CAAC,IAAI;oBACrC,gBAAgB;oBAChB,UAAU,UAAU,GAAG,aAAa;gBACtC;;YAEA,MAAM;0DAAgB;oBACpB,cAAc;oBACd,UAAU,KAAK,CAAC,MAAM,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,SAAS;gBACpF;;YAEA,MAAM;6DAAmB;oBACvB,cAAc;oBACd,UAAU,KAAK,CAAC,MAAM,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,SAAS;gBACpF;;YAEA,qBAAqB;YACrB,UAAU,KAAK,CAAC,MAAM,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG,SAAS;YAElF,UAAU,gBAAgB,CAAC,aAAa;YACxC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,UAAU,gBAAgB,CAAC,cAAc;YAEzC;4CAAO;oBACL,UAAU,mBAAmB,CAAC,aAAa;oBAC3C,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,WAAW;oBACxC,UAAU,mBAAmB,CAAC,cAAc;gBAC9C;;QACF;mCAAG;QAAC;QAAgB;QAAY;QAAQ;QAAY;KAAa;IAEjE,2DAA2D;IAC3D,MAAM,qBAAqB,CAAC,GAAqB;QAC/C,IAAI,eAAe,GAAG;YACpB,EAAE,cAAc;YAChB;QACF;QACA,qCAAqC;QACrC,OAAO,IAAI,CAAC,YAAY,UAAU;IACpC;IAEA,4DAA4D;IAC5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,UAAU,QAAQ,mCAAmC;YAE1D,MAAM;wDAAc;oBAClB,kBAAkB;oBAClB,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EACvC,KAAK,aAAa,EAClB,KAAK,SAAS,EACd,KAAK,UAAU,EACf,KAAK,OAAO;wBAEd,WAAW;oBACb,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;wBACzC,WAAW;oBACb,SAAU;wBACR,kBAAkB;oBACpB;gBACF;;YAEA;QACF;mCAAG;QAAC;QAAU,KAAK,aAAa;QAAE,KAAK,SAAS;QAAE,KAAK,UAAU;QAAE,KAAK,OAAO;KAAC;IAEhF,IAAI,CAAC,UAAU;QACb,OAAO,MAAM,gDAAgD;IAC/D;IAEA,uBAAuB;IACvB,MAAM,gBAAgB,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG;QAAE,WAAW;IAAK;IAEvF,yDAAyD;IAEzD,yBAAyB;IACzB,MAAM,oBAAoB;QACxB,IAAI,gBAAgB;YAClB,OAAO;QACT;QAEA,IAAI,SAAS;YACX,OAAO,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD,EAAE;QAC7B;QAEA,oCAAoC;QACpC,OAAO,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAC1B,KAAK,aAAa,EAClB,KAAK,SAAS,EACd,KAAK,UAAU,EACf,KAAK,OAAO;IAEhB;IAEA,qBAAqB;IACrB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO,QAAQ;gBACf,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAY;YACV,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;QACA,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uFACA,yDACA;;0BAIF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,aAAa,iBACrB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;gCAClC,QAAO;gCACP,KAAI;gCACJ,WAAU;;kDAEV,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,8HAAA,CAAA,cAAW;gDACV,KAAK,SAAS,QAAQ,IAAI;gDAC1B,KAAK,SAAS,aAAa,IAAI;gDAC/B,WAAU;;;;;;0DAEZ,6LAAC,8HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACxB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,aAAa;;;;;;4CAExB,SAAS,aAAa,kBACrB,6LAAC;gDAAI,WAAU;;oDAAwD;oDACnE,SAAS,aAAa;;;;;;;0DAG5B,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;;qDAKP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,8HAAA,CAAA,cAAW;gDACV,KAAK,SAAS,QAAQ,IAAI;gDAC1B,KAAK,SAAS,aAAa,IAAI;gDAC/B,WAAU;;;;;;0DAEZ,6LAAC,8HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACxB,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,SAAS,aAAa;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;0DACZ;;;;;;;;;;;;;;;;;;0CAMT,6LAAC,wIAAA,CAAA,eAAY;;kDACX,6LAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;kDAG9B,6LAAC,wIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DAEzC,6LAAC,wIAAA,CAAA,mBAAgB;gDACf,SAAS;gDACT,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAKpC,WAAW,CAAC,kCACX;;kEACE,6LAAC,wIAAA,CAAA,wBAAqB;;;;;kEACtB,6LAAC,wIAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,aAAa;wDAC5B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGpC,6LAAC,wIAAA,CAAA,wBAAqB;;;;;kEACtB,6LAAC,wIAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,oBAAoB;wDACnC,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAM;;;;;;;;;;;;;;;;;;0BAKX,6LAAC;gBAAI,WAAU;0BACZ,0BACC,6LAAC,yKAAA,CAAA,UAA0B;oBACzB,QAAQ,KAAK,EAAE;oBACf,gBAAgB;oBAChB,mBAAmB;oBACnB,iBAAiB,KAAK,SAAS;oBAC/B,QAAQ;oBACR,UAAU;;;;;2BAEV,iCACF,6LAAC;oBAAE,WAAU;8BACV;;;;;yCAGH,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE;oBAC9B,WAAU;8BAEV,cAAA,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;YAOR,KAAK,SAAS,IAAI,CACjB,iCACE,6LAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mDACA,wBAAwB,oCAAoC;gBAE9D,SAAS,wBAAwB,IAAM,uBAAuB,QAAQ;0BAErE,sCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA,gBAAgB,qBAChB,cAAc;4BAEhB,QAAQ,IAAM,gBAAgB;4BAC9B,SAAS;gCACP,cAAc;gCACd,gBAAgB;4BAClB;4BACA,OAAM;4BACN,UAAU,QAAQ;;;;;;wBAEnB,8BACC,6LAAC;4BAAI,WAAU;;;;;;;;;;;yCAInB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAI;4BACJ,IAAI;4BACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4CACA,gBAAgB,qBAChB,cAAc;4BAEhB,QAAQ,IAAM,gBAAgB;4BAC9B,SAAS;gCACP,cAAc;gCACd,gBAAgB;4BAClB;4BACA,OAAM;4BACN,UAAU,QAAQ;;;;;;wBAEnB,8BACC,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;qCAMvB,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,EAAE;gBAAG,WAAU;0BAC/C,cAAA,6LAAC;oBAAI,WAAU;8BACZ,sCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,SAAS;gCACnB,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4DACA,gBAAgB,qBAChB,cAAc;gCAEhB,QAAQ,IAAM,gBAAgB;gCAC9B,SAAS;oCACP,cAAc;oCACd,gBAAgB;gCAClB;gCACA,OAAM;gCACN,UAAU,QAAQ;;;;;;4BAEnB,8BACC,6LAAC;gCAAI,WAAU;;;;;;;;;;;6CAInB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,SAAS;gCACnB,KAAI;gCACJ,IAAI;gCACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4CACA,gBAAgB,qBAChB,cAAc;gCAEhB,QAAQ,IAAM,gBAAgB;gCAC9B,SAAS;oCACP,cAAc;oCACd,gBAAgB;gCAClB;gCACA,OAAM;gCACN,UAAU,QAAQ;;;;;;4BAEnB,8BACC,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;oBAO7B;YAGC,kBAAkB,MAAM,GAAG,KAAK,CAAC,2BAChC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAG,WAAU;;wCAA+D;wCACvD,eAAe,MAAM;wCAAC;;;;;;;;;;;;;wBAI3C,kCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;iDAIpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK;4BACL,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,OAAO;gCAAE,gBAAgB;gCAAQ,iBAAiB;4BAAO;sCAExD,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,6LAAC;wCACC,WAAU;wCACV,SAAS,CAAC;4CACR,IAAI,SAAS,aAAa,EAAE;gDAC1B,mBAAmB,GAAG,CAAC,CAAC,EAAE,SAAS,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;4CAC1F;wCACF;kDAEA,cAAA,6LAAC,wIAAA,CAAA,UAAe;4CACd,SAAS;gDACP,GAAG,OAAO;gDACV,uCAAuC;gDACvC,aAAa;gDACb,cAAc;gDACd,YAAY,QAAQ,UAAU,IAAI;gDAClC,MAAM,QAAQ,IAAI,IAAI;gDACtB,cAAc;gDACd,QAAQ,QAAQ,SAAS,GAAG;oDAAC,QAAQ,SAAS;iDAAC,GAAG;gDAClD,sBAAsB;gDACtB,YAAY,IAAI;gDAChB,YAAY,IAAI;4CAClB;4CACA,QAAQ;;;;;;;;;;;mCA5BP,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAwC/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+IAAA,CAAA,UAAW;oBACV,UAAU;oBACV,aAAa,CAAC,CAAC,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,IAAI,OAAO,EAAE;oBAClF,UAAU,CAAC,CAAC,CAAC,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,OAAO,EAAE;;;;;;;;;;;0BAK/D,6LAAC,+JAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,cAAc;gBACd,QAAQ,KAAK,EAAE;gBACf,aAAa;gBACb,iBAAiB;;;;;;YAIlB,yBAAyB,uBAAuB,KAAK,SAAS,kBAC7D,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;0BAEtC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,uBAAuB;4BACtC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAK,KAAK,SAAS;4BACnB,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GApnBwB;;QA4B2B,4JAAA,CAAA,mBAAgB;;;KA5B3C", "debugId": null}}, {"offset": {"line": 4873, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/UnifiedPostCard.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport ModernPostCard from './ModernPostCard';\r\nimport { PostWithBusinessProfile } from '@/lib/types/posts';\r\n\r\ninterface UnifiedPostCardProps {\r\n  post: UnifiedPost;\r\n  index?: number;\r\n  onPostUpdate?: (_postId: string, _newContent: string) => void;\r\n  onPostDelete?: (_postId: string) => void;\r\n  onProductsUpdate?: (_postId: string, _newProductIds: string[]) => void;\r\n  showActualAspectRatio?: boolean;\r\n  disablePostClick?: boolean;\r\n  enableImageFullscreen?: boolean;\r\n}\r\n\r\nexport default function UnifiedPostCard({\r\n  post,\r\n  index = 0,\r\n  onPostUpdate,\r\n  onPostDelete,\r\n  onProductsUpdate,\r\n  showActualAspectRatio = false,\r\n  disablePostClick = false,\r\n  enableImageFullscreen = false\r\n}: UnifiedPostCardProps) {\r\n\r\n  // Transform unified post to business post format for consistent rendering\r\n  // Now using author_name and author_avatar directly from the unified_posts view\r\n  const businessPost: PostWithBusinessProfile = {\r\n    id: post.id,\r\n    business_id: post.author_id,\r\n    content: post.content,\r\n    image_url: post.image_url,\r\n    created_at: post.created_at,\r\n    updated_at: post.updated_at,\r\n    city_slug: post.city_slug,\r\n    state_slug: post.state_slug,\r\n    locality_slug: post.locality_slug,\r\n    pincode: post.pincode,\r\n    product_ids: post.product_ids,\r\n    mentioned_business_ids: post.mentioned_business_ids,\r\n    business_profiles: {\r\n      id: post.author_id,\r\n      business_name: post.author_name || (post.post_source === 'customer' ? 'Customer' : 'Business'),\r\n      logo_url: post.author_avatar,\r\n      business_slug: post.business_slug, // Now available from unified_posts view\r\n      phone: post.phone, // Now available from unified_posts view\r\n      whatsapp_number: post.whatsapp_number, // Now available from unified_posts view\r\n      city: null,\r\n      state: null\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ModernPostCard\r\n      post={businessPost}\r\n      index={index}\r\n      onPostUpdate={onPostUpdate}\r\n      onPostDelete={onPostDelete}\r\n      onProductsUpdate={onProductsUpdate}\r\n      showActualAspectRatio={showActualAspectRatio}\r\n      disablePostClick={disablePostClick}\r\n      enableImageFullscreen={enableImageFullscreen}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAiBe,SAAS,gBAAgB,EACtC,IAAI,EACJ,QAAQ,CAAC,EACT,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,wBAAwB,KAAK,EAC7B,mBAAmB,KAAK,EACxB,wBAAwB,KAAK,EACR;IAErB,0EAA0E;IAC1E,+EAA+E;IAC/E,MAAM,eAAwC;QAC5C,IAAI,KAAK,EAAE;QACX,aAAa,KAAK,SAAS;QAC3B,SAAS,KAAK,OAAO;QACrB,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,eAAe,KAAK,aAAa;QACjC,SAAS,KAAK,OAAO;QACrB,aAAa,KAAK,WAAW;QAC7B,wBAAwB,KAAK,sBAAsB;QACnD,mBAAmB;YACjB,IAAI,KAAK,SAAS;YAClB,eAAe,KAAK,WAAW,IAAI,CAAC,KAAK,WAAW,KAAK,aAAa,aAAa,UAAU;YAC7F,UAAU,KAAK,aAAa;YAC5B,eAAe,KAAK,aAAa;YACjC,OAAO,KAAK,KAAK;YACjB,iBAAiB,KAAK,eAAe;YACrC,MAAM;YACN,OAAO;QACT;IACF;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAc;QACb,MAAM;QACN,OAAO;QACP,cAAc;QACd,cAAc;QACd,kBAAkB;QAClB,uBAAuB;QACvB,kBAAkB;QAClB,uBAAuB;;;;;;AAG7B;KAlDwB", "debugId": null}}, {"offset": {"line": 4935, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/PostCardSkeleton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface PostCardSkeletonProps {\r\n  index?: number;\r\n  showImage?: boolean;\r\n  showProducts?: boolean;\r\n}\r\n\r\nexport default function PostCardSkeleton({ \r\n  index = 0, \r\n  showImage = true, \r\n  showProducts = false \r\n}: PostCardSkeletonProps) {\r\n  const cardVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        duration: 0.4,\r\n        delay: index * 0.1,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={cardVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className={cn(\r\n        \"bg-white dark:bg-black rounded-xl border border-neutral-200 dark:border-neutral-800\",\r\n        \"shadow-sm overflow-hidden mb-4 md:mb-6\"\r\n      )}\r\n    >\r\n      {/* Header Skeleton */}\r\n      <div className=\"p-4 pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3 flex-1\">\r\n            {/* Avatar Skeleton */}\r\n            <Skeleton className=\"h-12 w-12 rounded-full\" />\r\n            \r\n            <div className=\"flex-1 space-y-2\">\r\n              {/* Business Name */}\r\n              <Skeleton className=\"h-4 w-32\" />\r\n              \r\n              {/* Location and Time */}\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Skeleton className=\"h-3 w-24\" />\r\n                <Skeleton className=\"h-3 w-1 rounded-full\" />\r\n                <Skeleton className=\"h-3 w-16\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* More Button */}\r\n          <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content Skeleton */}\r\n      <div className=\"px-4 pb-3 space-y-2\">\r\n        <Skeleton className=\"h-4 w-full\" />\r\n        <Skeleton className=\"h-4 w-3/4\" />\r\n        <Skeleton className=\"h-4 w-1/2\" />\r\n      </div>\r\n\r\n      {/* Image Skeleton */}\r\n      {showImage && (\r\n        <div className=\"relative w-full\">\r\n          <Skeleton className=\"w-full aspect-[4/3]\" />\r\n        </div>\r\n      )}\r\n\r\n      {/* Products Skeleton */}\r\n      {showProducts && (\r\n        <div className=\"px-4 pt-4 space-y-3\">\r\n          {/* Products Header */}\r\n          <div className=\"flex items-center gap-2\">\r\n            <Skeleton className=\"h-6 w-6 rounded-lg\" />\r\n            <Skeleton className=\"h-4 w-32\" />\r\n          </div>\r\n          \r\n          {/* Products Grid */}\r\n          <div className=\"grid grid-cols-2 gap-3\">\r\n            {Array.from({ length: 2 }).map((_, i) => (\r\n              <div key={i} className=\"bg-neutral-50 dark:bg-neutral-900 rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden\">\r\n                <Skeleton className=\"w-full aspect-square\" />\r\n                <div className=\"p-3 space-y-2\">\r\n                  <Skeleton className=\"h-4 w-full\" />\r\n                  <Skeleton className=\"h-4 w-16\" />\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Actions Skeleton */}\r\n      <div className=\"p-4 pt-3\">\r\n        <div className=\"flex gap-3\">\r\n          <Skeleton className=\"h-10 flex-1\" />\r\n          <Skeleton className=\"h-10 flex-1\" />\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS,iBAAiB,EACvC,QAAQ,CAAC,EACT,YAAY,IAAI,EAChB,eAAe,KAAK,EACE;IACtB,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,OAAO,QAAQ;gBACf,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uFACA;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDAGpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,gIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAM1B,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;YAIrB,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;YAKvB,8BACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gCAAY,WAAU;;kDACrB,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC,gIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;+BAJd;;;;;;;;;;;;;;;;0BAalB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK9B;KApGwB", "debugId": null}}, {"offset": {"line": 5227, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/FilterPills.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { FeedFilterType } from '@/lib/types/posts';\r\nimport { cn } from '@/lib/utils';\r\nimport { Sparkles, Users, MapPin, Hash, Building, Globe, List } from 'lucide-react';\r\n\r\ninterface FilterPillsProps {\r\n  activeFilter: FeedFilterType;\r\n  onFilterChange: (_filter: FeedFilterType) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst filterOptions = [\r\n  { value: 'smart' as FeedFilterType, label: 'Smart Feed', icon: Sparkles },\r\n  { value: 'subscribed' as FeedFilterType, label: 'Following', icon: Users },\r\n  { value: 'locality' as FeedFilterType, label: 'Locality', icon: MapPin },\r\n  { value: 'pincode' as FeedFilterType, label: 'Pincode', icon: Hash },\r\n  { value: 'city' as FeedFilterType, label: 'City', icon: Building },\r\n  { value: 'state' as FeedFilterType, label: 'State', icon: Globe },\r\n  { value: 'all' as FeedFilterType, label: 'All Posts', icon: List },\r\n];\r\n\r\nexport default function FilterPills({ activeFilter, onFilterChange, isLoading = false }: FilterPillsProps) {\r\n  return (\r\n    <div className=\"w-full\">\r\n      {/* Mobile: Horizontal scroll */}\r\n      <div className=\"flex gap-2 overflow-x-auto scrollbar-hide pb-2 md:pb-0\">\r\n        <div className=\"flex gap-2 md:flex-wrap md:justify-center\">\r\n          {filterOptions.map((option) => {\r\n            const Icon = option.icon;\r\n            const isActive = activeFilter === option.value;\r\n\r\n            return (\r\n              <motion.button\r\n                key={option.value}\r\n                onClick={() => !isLoading && onFilterChange(option.value)}\r\n                disabled={isLoading}\r\n                whileHover={{ scale: 1.02 }}\r\n                whileTap={{ scale: 0.98 }}\r\n                className={cn(\r\n                  \"flex items-center gap-2 px-4 py-2.5 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap relative cursor-pointer\",\r\n                  \"border border-neutral-200 dark:border-neutral-700\",\r\n                  isActive\r\n                    ? \"bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)] shadow-md\"\r\n                    : \"bg-white dark:bg-black text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-900 hover:border-neutral-300 dark:hover:border-neutral-600\",\r\n                  isLoading && \"opacity-50 cursor-not-allowed\"\r\n                )}\r\n              >\r\n                <Icon className=\"h-4 w-4\" />\r\n                <span>{option.label}</span>\r\n\r\n                {/* Active indicator */}\r\n                {isActive && (\r\n                  <motion.div\r\n                    layoutId=\"activeFilter\"\r\n                    className=\"absolute inset-0 bg-[var(--brand-gold)] rounded-full -z-10\"\r\n                    initial={false}\r\n                    transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\r\n                  />\r\n                )}\r\n              </motion.button>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAaA,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAA2B,OAAO;QAAc,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACxE;QAAE,OAAO;QAAgC,OAAO;QAAa,MAAM,uMAAA,CAAA,QAAK;IAAC;IACzE;QAAE,OAAO;QAA8B,OAAO;QAAY,MAAM,6MAAA,CAAA,SAAM;IAAC;IACvE;QAAE,OAAO;QAA6B,OAAO;QAAW,MAAM,qMAAA,CAAA,OAAI;IAAC;IACnE;QAAE,OAAO;QAA0B,OAAO;QAAQ,MAAM,6MAAA,CAAA,WAAQ;IAAC;IACjE;QAAE,OAAO;QAA2B,OAAO;QAAS,MAAM,uMAAA,CAAA,QAAK;IAAC;IAChE;QAAE,OAAO;QAAyB,OAAO;QAAa,MAAM,qMAAA,CAAA,OAAI;IAAC;CAClE;AAEc,SAAS,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY,KAAK,EAAoB;IACvG,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC;oBAClB,MAAM,OAAO,OAAO,IAAI;oBACxB,MAAM,WAAW,iBAAiB,OAAO,KAAK;oBAE9C,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,SAAS,IAAM,CAAC,aAAa,eAAe,OAAO,KAAK;wBACxD,UAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8IACA,qDACA,WACI,oGACA,sKACJ,aAAa;;0CAGf,6LAAC;gCAAK,WAAU;;;;;;0CAChB,6LAAC;0CAAM,OAAO,KAAK;;;;;;4BAGlB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,UAAS;gCACT,WAAU;gCACV,SAAS;gCACT,YAAY;oCAAE,MAAM;oCAAU,WAAW;oCAAK,SAAS;gCAAG;;;;;;;uBAvBzD,OAAO,KAAK;;;;;gBA4BvB;;;;;;;;;;;;;;;;AAKV;KA7CwB", "debugId": null}}, {"offset": {"line": 5366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernFeedHeader.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { FeedFilterType } from '@/lib/types/posts';\r\nimport FilterPills from './FilterPills';\r\n\r\ninterface ModernFeedHeaderProps {\r\n  activeFilter: FeedFilterType;\r\n  onFilterChange: (_filter: FeedFilterType) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ModernFeedHeader({\r\n  activeFilter,\r\n  onFilterChange,\r\n  isLoading = false\r\n}: ModernFeedHeaderProps) {\r\n  return (\r\n    <div className=\"mb-8\">\r\n      {/* Filter Section */}\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <h2 className=\"text-lg font-semibold text-foreground\">\r\n          Your Feed\r\n        </h2>\r\n        <div className=\"text-sm text-muted-foreground\">\r\n          Choose your feed preference\r\n        </div>\r\n      </div>\r\n\r\n      <FilterPills\r\n        activeFilter={activeFilter}\r\n        onFilterChange={onFilterChange}\r\n        isLoading={isLoading}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAYe,SAAS,iBAAiB,EACvC,YAAY,EACZ,cAAc,EACd,YAAY,KAAK,EACK;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAI,WAAU;kCAAgC;;;;;;;;;;;;0BAKjD,6LAAC,+IAAA,CAAA,UAAW;gBACV,cAAc;gBACd,gBAAgB;gBAChB,WAAW;;;;;;;;;;;;AAInB;KAxBwB", "debugId": null}}, {"offset": {"line": 5431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernFeedContainer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { motion } from 'framer-motion';\r\nimport { ReactNode } from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface ModernFeedContainerProps {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport default function ModernFeedContainer({ children, className }: ModernFeedContainerProps) {\r\n  return (\r\n    <div className={cn(\"min-h-screen bg-background\", className)}>\r\n      <div className=\"w-full px-4 sm:px-6 lg:px-8 py-6\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AAWe,SAAS,oBAAoB,EAAE,QAAQ,EAAE,SAAS,EAA4B;IAC3F,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC/C,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;KARwB", "debugId": null}}, {"offset": {"line": 5468, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 5583, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 5617, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,iKAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,iKAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,iKAAA,CAAA,iBAAc;QACtB,iKAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAErB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,6HAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,mKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 5819, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerPosts/crud.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/client';\r\nimport { CustomerPostFormData } from '@/lib/types/posts';\r\n\r\nexport interface ActionResponse {\r\n  success: boolean;\r\n  message: string;\r\n  error?: string;\r\n  data?: unknown;\r\n}\r\n\r\n/**\r\n * Create a new customer post\r\n */\r\nexport async function createCustomerPost(formData: CustomerPostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to create a post'\r\n    };\r\n  }\r\n\r\n  // Get the user's customer profile\r\n  const { data: customerProfile, error: profileError } = await supabase\r\n    .from('customer_profiles')\r\n    .select('id, city_slug, state_slug, locality_slug, pincode')\r\n    .eq('id', user.id)\r\n    .single();\r\n\r\n  if (profileError || !customerProfile) {\r\n    return {\r\n      success: false,\r\n      message: 'Customer profile not found',\r\n      error: 'You must have a customer profile to create a post'\r\n    };\r\n  }\r\n\r\n  // Prepare post data\r\n  const postData = {\r\n    customer_id: user.id,\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    city_slug: customerProfile.city_slug,\r\n    state_slug: customerProfile.state_slug,\r\n    locality_slug: customerProfile.locality_slug,\r\n    pincode: customerProfile.pincode,\r\n    mentioned_business_ids: formData.mentioned_business_ids || []\r\n  };\r\n\r\n  // Insert the post\r\n  const { data, error } = await supabase\r\n    .from('customer_posts')\r\n    .insert(postData)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error creating customer post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to create post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post created successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Update an existing customer post\r\n */\r\nexport async function updateCustomerPost(postId: string, formData: CustomerPostFormData): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to update a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('customer_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('customer_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to update it'\r\n    };\r\n  }\r\n\r\n  // Prepare update data\r\n  const updateData = {\r\n    content: formData.content,\r\n    image_url: formData.image_url || null,\r\n    mentioned_business_ids: formData.mentioned_business_ids || [],\r\n    updated_at: new Date().toISOString()\r\n  };\r\n\r\n  // Update the post\r\n  const { data, error } = await supabase\r\n    .from('customer_posts')\r\n    .update(updateData)\r\n    .eq('id', postId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating customer post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to update post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post updated successfully',\r\n    data\r\n  };\r\n}\r\n\r\n/**\r\n * Delete a customer post\r\n */\r\nexport async function deleteCustomerPost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n  if (userError || !user) {\r\n    return {\r\n      success: false,\r\n      message: 'Authentication required',\r\n      error: 'You must be logged in to delete a post'\r\n    };\r\n  }\r\n\r\n  // Check if the post exists and belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('customer_posts')\r\n    .select('id')\r\n    .eq('id', postId)\r\n    .eq('customer_id', user.id)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      message: 'Post not found',\r\n      error: 'The post does not exist or you do not have permission to delete it'\r\n    };\r\n  }\r\n\r\n  // Delete the post\r\n  const { error } = await supabase\r\n    .from('customer_posts')\r\n    .delete()\r\n    .eq('id', postId);\r\n\r\n  if (error) {\r\n    console.error('Error deleting customer post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to delete post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post deleted successfully'\r\n  };\r\n}\r\n\r\n/**\r\n * Get customer posts for a specific customer\r\n */\r\nexport async function getCustomerPosts(customerId?: string, page: number = 1, limit: number = 10): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  let query = supabase\r\n    .from('customer_posts')\r\n    .select(`\r\n      *,\r\n      customer_profiles (\r\n        id,\r\n        name,\r\n        avatar_url,\r\n        city,\r\n        state,\r\n        locality\r\n      )\r\n    `)\r\n    .order('created_at', { ascending: false });\r\n\r\n  // If customerId is provided, filter by it\r\n  if (customerId) {\r\n    query = query.eq('customer_id', customerId);\r\n  }\r\n\r\n  // Add pagination\r\n  const from = (page - 1) * limit;\r\n  const to = from + limit - 1;\r\n  query = query.range(from, to);\r\n\r\n  const { data, error, count } = await query;\r\n\r\n  if (error) {\r\n    console.error('Error fetching customer posts:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to fetch posts',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Posts fetched successfully',\r\n    data: {\r\n      items: data || [],\r\n      totalCount: count || 0,\r\n      hasMore: data ? data.length === limit : false\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Get a single customer post by ID\r\n */\r\nexport async function getCustomerPost(postId: string): Promise<ActionResponse> {\r\n  const supabase = await createClient();\r\n\r\n  const { data, error } = await supabase\r\n    .from('customer_posts')\r\n    .select(`\r\n      *,\r\n      customer_profiles (\r\n        id,\r\n        name,\r\n        avatar_url,\r\n        city,\r\n        state,\r\n        locality\r\n      )\r\n    `)\r\n    .eq('id', postId)\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error fetching customer post:', error);\r\n    return {\r\n      success: false,\r\n      message: 'Failed to fetch post',\r\n      error: error.message\r\n    };\r\n  }\r\n\r\n  return {\r\n    success: true,\r\n    message: 'Post fetched successfully',\r\n    data\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAaO,eAAe,mBAAmB,QAA8B;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,qDACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,gBAAgB,CAAC,iBAAiB;QACpC,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW;QACf,aAAa,KAAK,EAAE;QACpB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,WAAW,gBAAgB,SAAS;QACpC,YAAY,gBAAgB,UAAU;QACtC,eAAe,gBAAgB,aAAa;QAC5C,SAAS,gBAAgB,OAAO;QAChC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;IAC/D;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,MAAc,EAAE,QAA8B;IACrF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa;QACjB,SAAS,SAAS,OAAO;QACzB,WAAW,SAAS,SAAS,IAAI;QACjC,wBAAwB,SAAS,sBAAsB,IAAI,EAAE;QAC7D,YAAY,IAAI,OAAO,WAAW;IACpC;IAEA,kBAAkB;IAClB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,MAAc;IACrD,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAExE,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,kBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,EAAE,CAAC,eAAe,KAAK,EAAE,EACzB,MAAM;IAET,IAAI,aAAa,CAAC,cAAc;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO;QACT;IACF;IAEA,kBAAkB;IAClB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;IAEZ,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;IACX;AACF;AAKO,eAAe,iBAAiB,UAAmB,EAAE,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9F,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,IAAI,QAAQ,SACT,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM;IAE1C,0CAA0C;IAC1C,IAAI,YAAY;QACd,QAAQ,MAAM,EAAE,CAAC,eAAe;IAClC;IAEA,iBAAiB;IACjB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;IAC1B,MAAM,KAAK,OAAO,QAAQ;IAC1B,QAAQ,MAAM,KAAK,CAAC,MAAM;IAE1B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;IAErC,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT,MAAM;YACJ,OAAO,QAAQ,EAAE;YACjB,YAAY,SAAS;YACrB,SAAS,OAAO,KAAK,MAAM,KAAK,QAAQ;QAC1C;IACF;AACF;AAKO,eAAe,gBAAgB,MAAc;IAClD,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,CAAC;;;;;;;;;;IAUT,CAAC,EACA,EAAE,CAAC,MAAM,QACT,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,SAAS;YACT,SAAS;YACT,OAAO,MAAM,OAAO;QACtB;IACF;IAEA,OAAO;QACL,SAAS;QACT,SAAS;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 6031, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/customerPosts/index.ts"], "sourcesContent": ["// Export all customer post actions\r\nexport {\r\n  createCustomerPost,\r\n  updateCustomerPost,\r\n  deleteCustomerPost,\r\n  getCustomerPosts,\r\n  getCustomerPost,\r\n  type ActionResponse\r\n} from './crud';\r\n"], "names": [], "mappings": "AAAA,mCAAmC;;AACnC", "debugId": null}}, {"offset": {"line": 6053, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/types/posts.ts"], "sourcesContent": ["/**\r\n * Types for business posts, customer posts, and feed functionality\r\n */\r\n\r\nimport { z } from \"zod\";\r\n\r\n// Profile types enum (kept for future compatibility)\r\nexport const ProfileType = {\r\n  BUSINESS: 'business',\r\n  CUSTOMER: 'customer',\r\n} as const;\r\n\r\nexport type ProfileTypeValue = typeof ProfileType[keyof typeof ProfileType];\r\n\r\n// Business post schema for validation\r\nexport const postSchema = z.object({\r\n  id: z.string().uuid().optional(),\r\n  business_id: z.string().uuid().optional(),\r\n  content: z.string().min(1, { message: \"Post content is required\" }).max(2000, { message: \"Post content cannot exceed 2000 characters\" }),\r\n  image_url: z.union([\r\n    z.string().url({ message: \"Invalid image URL\" }),\r\n    z.literal(\"\"),\r\n    z.null(),\r\n    z.undefined()\r\n  ]).optional().nullable(),\r\n  created_at: z.string().optional(),\r\n  updated_at: z.string().optional(),\r\n  city_slug: z.string().optional(),\r\n  state_slug: z.string().optional(),\r\n  locality_slug: z.string().optional(),\r\n  pincode: z.string().optional(),\r\n  product_ids: z.array(z.string().uuid())\r\n    .max(5, { message: \"You can link up to 5 products per post\" })\r\n    .default([]),\r\n  mentioned_business_ids: z.array(z.string().uuid())\r\n    .max(10, { message: \"You can mention up to 10 businesses per post\" })\r\n    .default([]),\r\n});\r\n\r\n// Type for post data from form\r\nexport type PostFormData = z.infer<typeof postSchema>;\r\n\r\n// Type for post data from database\r\nexport interface PostData {\r\n  id: string;\r\n  business_id: string;\r\n  content: string;\r\n  image_url: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  city_slug: string | null;\r\n  state_slug: string | null;\r\n  locality_slug: string | null;\r\n  pincode: string | null;\r\n  product_ids: string[];\r\n  mentioned_business_ids: string[];\r\n}\r\n\r\n// Type for post data with business profile information\r\nexport interface PostWithBusinessProfile extends PostData {\r\n  business_profiles?: {\r\n    id: string;\r\n    business_name: string | null;\r\n    logo_url: string | null;\r\n    business_slug: string | null;\r\n    phone: string | null;\r\n    whatsapp_number: string | null;\r\n    city: string | null;\r\n    state: string | null;\r\n  };\r\n}\r\n\r\n// Type for product data in posts\r\nexport interface ProductData {\r\n  id: string;\r\n  name: string;\r\n  base_price: number | null;\r\n  discounted_price: number | null;\r\n  image_url: string | null;\r\n  slug: string | null;\r\n}\r\n\r\n// Type for post data with linked products\r\nexport interface PostWithProducts extends PostData {\r\n  linked_products?: ProductData[];\r\n}\r\n\r\n// Type for feed items (currently only business posts)\r\nexport type FeedItem = PostWithBusinessProfile & Partial<PostWithProducts>;\r\n\r\n// Type for feed filter options\r\nexport type FeedFilterType = 'smart' | 'subscribed' | 'locality' | 'pincode' | 'city' | 'state' | 'all';\r\n\r\n// Type for feed query parameters\r\nexport interface FeedQueryParams {\r\n  filter?: FeedFilterType;\r\n  page?: number;\r\n  limit?: number;\r\n  city_slug?: string;\r\n  state_slug?: string;\r\n  locality_slug?: string;\r\n  pincode?: string;\r\n}\r\n\r\n// ============================================================================\r\n// CUSTOMER POSTS TYPES\r\n// ============================================================================\r\n\r\n\r\n\r\n// Customer post schema for validation\r\nexport const customerPostSchema = z.object({\r\n  id: z.string().uuid().optional(),\r\n  customer_id: z.string().uuid().optional(),\r\n  content: z.string().min(1, { message: \"Post content is required\" }).max(2000, { message: \"Post content cannot exceed 2000 characters\" }),\r\n  image_url: z.union([\r\n    z.string().url({ message: \"Invalid image URL\" }),\r\n    z.literal(\"\"),\r\n    z.null(),\r\n    z.undefined()\r\n  ]).optional().nullable(),\r\n  created_at: z.string().optional(),\r\n  updated_at: z.string().optional(),\r\n  city_slug: z.string().optional(),\r\n  state_slug: z.string().optional(),\r\n  locality_slug: z.string().optional(),\r\n  pincode: z.string().optional(),\r\n  mentioned_business_ids: z.array(z.string().uuid())\r\n    .max(10, { message: \"You can mention up to 10 businesses per post\" })\r\n    .default([]),\r\n});\r\n\r\n// Type for customer post data from form\r\nexport type CustomerPostFormData = z.infer<typeof customerPostSchema>;\r\n\r\n// Type for customer post data from database\r\nexport interface CustomerPostData {\r\n  id: string;\r\n  customer_id: string;\r\n  content: string;\r\n  image_url: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  city_slug: string | null;\r\n  state_slug: string | null;\r\n  locality_slug: string | null;\r\n  pincode: string | null;\r\n  mentioned_business_ids: string[];\r\n}\r\n\r\n// Type for customer post data with customer profile information\r\nexport interface CustomerPostWithProfile extends CustomerPostData {\r\n  customer_profiles?: {\r\n    id: string;\r\n    name: string | null;\r\n    avatar_url: string | null;\r\n    city: string | null;\r\n    state: string | null;\r\n    locality: string | null;\r\n  };\r\n}\r\n\r\n// Type for customer post data with mentioned businesses\r\nexport interface CustomerPostWithBusinesses extends CustomerPostData {\r\n  mentioned_businesses?: {\r\n    id: string;\r\n    business_name: string | null;\r\n    logo_url: string | null;\r\n    business_slug: string | null;\r\n    city: string | null;\r\n    state: string | null;\r\n  }[];\r\n}\r\n\r\n// Combined type for customer posts with all related data\r\nexport type CustomerFeedItem = CustomerPostWithProfile & Partial<CustomerPostWithBusinesses>;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAED;;AAGO,MAAM,cAAc;IACzB,UAAU;IACV,UAAU;AACZ;AAKO,MAAM,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IACvC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAAG,GAAG,CAAC,MAAM;QAAE,SAAS;IAA6C;IACtI,WAAW,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QACjB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;YAAE,SAAS;QAAoB;QAC9C,uIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QACV,uIAAA,CAAA,IAAC,CAAC,IAAI;QACN,uIAAA,CAAA,IAAC,CAAC,SAAS;KACZ,EAAE,QAAQ,GAAG,QAAQ;IACtB,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,aAAa,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,IACjC,GAAG,CAAC,GAAG;QAAE,SAAS;IAAyC,GAC3D,OAAO,CAAC,EAAE;IACb,wBAAwB,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,IAC5C,GAAG,CAAC,IAAI;QAAE,SAAS;IAA+C,GAClE,OAAO,CAAC,EAAE;AACf;AA0EO,MAAM,qBAAqB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,IAAI,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IAC9B,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,GAAG,QAAQ;IACvC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAAG,GAAG,CAAC,MAAM;QAAE,SAAS;IAA6C;IACtI,WAAW,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QACjB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;YAAE,SAAS;QAAoB;QAC9C,uIAAA,CAAA,IAAC,CAAC,OAAO,CAAC;QACV,uIAAA,CAAA,IAAC,CAAC,IAAI;QACN,uIAAA,CAAA,IAAC,CAAC,SAAS;KACZ,EAAE,QAAQ,GAAG,QAAQ;IACtB,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,WAAW,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,YAAY,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,wBAAwB,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,IAAI,IAC5C,GAAG,CAAC,IAAI;QAAE,SAAS;IAA+C,GAClE,OAAO,CAAC,EAAE;AACf", "debugId": null}}, {"offset": {"line": 6130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/forms/LocationDisplay.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { MapPin, Loader2 } from 'lucide-react';\r\nimport { createClient } from '@/utils/supabase/client';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface LocationDisplayProps {\r\n  className?: string;\r\n}\r\n\r\ninterface LocationData {\r\n  city?: string;\r\n  state?: string;\r\n  locality?: string;\r\n  pincode?: string;\r\n}\r\n\r\nexport default function LocationDisplay({ className }: LocationDisplayProps) {\r\n  const [location, setLocation] = useState<LocationData | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchUserLocation = async () => {\r\n      try {\r\n        const supabase = createClient();\r\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\r\n\r\n        if (userError || !user) {\r\n          setError('Authentication required');\r\n          return;\r\n        }\r\n\r\n        // Try to get business profile first, then customer profile\r\n        const { data: businessProfile } = await supabase\r\n          .from('business_profiles')\r\n          .select('city, state, locality, pincode')\r\n          .eq('id', user.id)\r\n          .single();\r\n\r\n        if (businessProfile) {\r\n          setLocation(businessProfile);\r\n          return;\r\n        }\r\n\r\n        // Fallback to customer profile\r\n        const { data: customerProfile } = await supabase\r\n          .from('customer_profiles')\r\n          .select('city, state, locality, pincode')\r\n          .eq('id', user.id)\r\n          .single();\r\n\r\n        if (customerProfile) {\r\n          setLocation(customerProfile);\r\n        } else {\r\n          setError('Location not found in profile');\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching location:', err);\r\n        setError('Failed to load location');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserLocation();\r\n  }, []);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={cn(\r\n        \"flex items-center gap-2 text-sm text-muted-foreground\",\r\n        className\r\n      )}>\r\n        <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n        <span>Loading location...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error || !location) {\r\n    return (\r\n      <div className={cn(\r\n        \"flex items-center gap-2 text-sm text-muted-foreground\",\r\n        className\r\n      )}>\r\n        <MapPin className=\"h-4 w-4\" />\r\n        <span>Location not available</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Format location string\r\n  const locationParts = [\r\n    location.locality,\r\n    location.city,\r\n    location.state,\r\n    location.pincode\r\n  ].filter(Boolean);\r\n\r\n  const locationString = locationParts.join(', ');\r\n\r\n  return (\r\n    <div className={cn(\r\n      \"flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 px-3 py-2 rounded-lg border\",\r\n      className\r\n    )}>\r\n      <MapPin className=\"h-4 w-4 text-[var(--brand-gold)]\" />\r\n      <span>\r\n        <span className=\"font-medium\">Posting from:</span> {locationString}\r\n      </span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAkBe,SAAS,gBAAgB,EAAE,SAAS,EAAwB;;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;+DAAoB;oBACxB,IAAI;wBACF,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;wBAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;wBAExE,IAAI,aAAa,CAAC,MAAM;4BACtB,SAAS;4BACT;wBACF;wBAEA,2DAA2D;wBAC3D,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,kCACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,IAAI,iBAAiB;4BACnB,YAAY;4BACZ;wBACF;wBAEA,+BAA+B;wBAC/B,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,kCACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;wBAET,IAAI,iBAAiB;4BACnB,YAAY;wBACd,OAAO;4BACL,SAAS;wBACX;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,SAAS;oBACX,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;oCAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,yDACA;;8BAEA,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,6LAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,IAAI,SAAS,CAAC,UAAU;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,yDACA;;8BAEA,6LAAC,6MAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,6LAAC;8BAAK;;;;;;;;;;;;IAGZ;IAEA,yBAAyB;IACzB,MAAM,gBAAgB;QACpB,SAAS,QAAQ;QACjB,SAAS,IAAI;QACb,SAAS,KAAK;QACd,SAAS,OAAO;KACjB,CAAC,MAAM,CAAC;IAET,MAAM,iBAAiB,cAAc,IAAI,CAAC;IAE1C,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,iGACA;;0BAEA,6LAAC,6MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,6LAAC;;kCACC,6LAAC;wBAAK,WAAU;kCAAc;;;;;;oBAAoB;oBAAE;;;;;;;;;;;;;AAI5D;GAhGwB;KAAA", "debugId": null}}, {"offset": {"line": 6292, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/forms/MediaUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport { Upload, X, Loader2, ImageIcon } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport interface MediaUploadProps {\r\n  previewUrl: string | null;\r\n  isUploading: boolean;\r\n  uploadError: string | null;\r\n  onFileSelect: (_file: File | null) => void;\r\n  onClearImage: () => void;\r\n  disabled?: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport default function MediaUpload({\r\n  previewUrl,\r\n  isUploading,\r\n  uploadError,\r\n  onFileSelect,\r\n  onClearImage,\r\n  disabled = false,\r\n  className,\r\n}: MediaUploadProps) {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0] || null;\r\n    onFileSelect(file);\r\n  };\r\n\r\n  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {\r\n    event.preventDefault();\r\n    if (disabled || isUploading) return;\r\n\r\n    const droppedFile = event.dataTransfer.files?.[0] || null;\r\n    if (droppedFile && droppedFile.type.startsWith(\"image/\")) {\r\n      onFileSelect(droppedFile);\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\r\n    event.preventDefault();\r\n  };\r\n\r\n  const openFileDialog = () => {\r\n    if (disabled || isUploading) return;\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleRemoveImage = () => {\r\n    onClearImage();\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = \"\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\"space-y-2\", className)}>\r\n      {/* Hidden file input */}\r\n      <input\r\n        ref={fileInputRef}\r\n        type=\"file\"\r\n        accept=\"image/jpeg,image/jpg,image/png,image/webp,image/gif,.jpg,.jpeg,.png,.webp,.gif\"\r\n        onChange={handleFileInputChange}\r\n        className=\"hidden\"\r\n        disabled={disabled || isUploading}\r\n      />\r\n\r\n      {/* Upload area or preview */}\r\n      {previewUrl ? (\r\n        // Image preview\r\n        <Card className=\"relative overflow-hidden\">\r\n          <CardContent className=\"p-0\">\r\n            <div className=\"relative w-full max-w-md mx-auto\">\r\n              <Image\r\n                src={previewUrl}\r\n                alt=\"Post image preview\"\r\n                width={400}\r\n                height={300}\r\n                className=\"object-contain rounded-lg w-full h-auto\"\r\n                sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n              />\r\n\r\n              {/* Remove button */}\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"destructive\"\r\n                size=\"icon\"\r\n                className=\"absolute top-2 right-2 h-8 w-8\"\r\n                onClick={handleRemoveImage}\r\n                disabled={disabled || isUploading}\r\n              >\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n\r\n              {/* Upload overlay */}\r\n              {isUploading && (\r\n                <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg\">\r\n                  <div className=\"text-white text-center\">\r\n                    <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-2\" />\r\n                    <p className=\"text-sm\">Uploading...</p>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      ) : (\r\n        // Upload area\r\n        <Card\r\n          className={cn(\r\n            \"border-2 border-dashed transition-colors cursor-pointer\",\r\n            \"hover:border-primary/50 hover:bg-muted/50\",\r\n            disabled || isUploading ? \"opacity-50 cursor-not-allowed\" : \"\",\r\n            uploadError ? \"border-destructive\" : \"border-muted-foreground/25\"\r\n          )}\r\n          onClick={openFileDialog}\r\n          onDrop={handleDrop}\r\n          onDragOver={handleDragOver}\r\n        >\r\n          <CardContent className=\"flex flex-col items-center justify-center p-8 text-center\">\r\n            {isUploading ? (\r\n              <>\r\n                <Loader2 className=\"h-12 w-12 animate-spin text-muted-foreground mb-4\" />\r\n                <p className=\"text-sm text-muted-foreground\">Processing image...</p>\r\n              </>\r\n            ) : (\r\n              <>\r\n                <div className=\"rounded-full bg-muted p-4 mb-4\">\r\n                  {uploadError ? (\r\n                    <ImageIcon className=\"h-8 w-8 text-destructive\" />\r\n                  ) : (\r\n                    <Upload className=\"h-8 w-8 text-muted-foreground\" />\r\n                  )}\r\n                </div>\r\n\r\n                <div className=\"space-y-2\">\r\n                  <p className=\"text-sm font-medium\">\r\n                    {uploadError ? \"Upload failed\" : \"Upload an image\"}\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    Drag and drop or click to select\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    JPEG, PNG, WebP, GIF • Max 15MB\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground/70\">\r\n                    Recommended: Square images for best display\r\n                  </p>\r\n                </div>\r\n              </>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {uploadError && (\r\n        <p className=\"text-sm text-destructive\">{uploadError}</p>\r\n      )}\r\n\r\n      {/* Replace image button when preview exists */}\r\n      {previewUrl && !isUploading && (\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={openFileDialog}\r\n          disabled={disabled}\r\n          className=\"w-full\"\r\n        >\r\n          <Upload className=\"h-4 w-4 mr-2\" />\r\n          Replace Image\r\n        </Button>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAmBe,SAAS,YAAY,EAClC,UAAU,EACV,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,WAAW,KAAK,EAChB,SAAS,EACQ;;IACjB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,wBAAwB,CAAC;QAC7B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QACxC,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,IAAI,YAAY,aAAa;QAE7B,MAAM,cAAc,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QACrD,IAAI,eAAe,YAAY,IAAI,CAAC,UAAU,CAAC,WAAW;YACxD,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;IACtB;IAEA,MAAM,iBAAiB;QACrB,IAAI,YAAY,aAAa;QAC7B,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,oBAAoB;QACxB;QACA,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,6LAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAO;gBACP,UAAU;gBACV,WAAU;gBACV,UAAU,YAAY;;;;;;YAIvB,aACC,gBAAgB;0BAChB,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,OAAM;;;;;;0CAIR,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,UAAU,YAAY;0CAEtB,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;4BAId,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAQnC,cAAc;0BACd,6LAAC,4HAAA,CAAA,OAAI;gBACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2DACA,6CACA,YAAY,cAAc,kCAAkC,IAC5D,cAAc,uBAAuB;gBAEvC,SAAS;gBACT,QAAQ;gBACR,YAAY;0BAEZ,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;8BACpB,4BACC;;0CACE,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;qDAG/C;;0CACE,6LAAC;gCAAI,WAAU;0CACZ,4BACC,6LAAC,2MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAItB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,cAAc,kBAAkB;;;;;;kDAEnC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;YAW3D,6BACC,6LAAC;gBAAE,WAAU;0BAA4B;;;;;;YAI1C,cAAc,CAAC,6BACd,6LAAC,8HAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,UAAU;gBACV,WAAU;;kCAEV,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAM7C;GAnKwB;KAAA", "debugId": null}}, {"offset": {"line": 6593, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/shared/upload-customer-post-media.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomerPostImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomerPostMediaUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload and process image for customer post\r\n * Future-proof structure: {userId}/posts/{year}/{month}/{postId}/image_0_{timestamp}.webp\r\n */\r\nexport async function uploadCustomerPostImage(\r\n  formData: FormData,\r\n  postId: string,\r\n  postCreatedAt?: string\r\n): Promise<CustomerPostMediaUploadResult> {\r\n  const supabase = await createClient();\r\n\r\n  // Get the current user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const userId = user.id;\r\n  const imageFile = formData.get(\"imageFile\") as File | null;\r\n\r\n  if (!imageFile) {\r\n    return { success: false, error: \"No image file provided.\" };\r\n  }\r\n\r\n  // Validate file type\r\n  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];\r\n  if (!allowedTypes.includes(imageFile.type)) {\r\n    return {\r\n      success: false,\r\n      error: \"Invalid file type. Only JPEG, PNG, WebP, and GIF images are allowed.\"\r\n    };\r\n  }\r\n\r\n  // Validate file size (15MB limit)\r\n  const maxSize = 15 * 1024 * 1024; // 15MB\r\n  if (imageFile.size > maxSize) {\r\n    return {\r\n      success: false,\r\n      error: \"File size exceeds 15MB limit.\"\r\n    };\r\n  }\r\n\r\n  // Validate that the post belongs to the user\r\n  const { data: existingPost, error: postError } = await supabase\r\n    .from('customer_posts')\r\n    .select('id, customer_id')\r\n    .eq('id', postId)\r\n    .eq('customer_id', userId)\r\n    .single();\r\n\r\n  if (postError || !existingPost) {\r\n    return {\r\n      success: false,\r\n      error: \"Post not found or you don't have permission to upload images for this post.\"\r\n    };\r\n  }\r\n\r\n  try {\r\n    // Create scalable path structure for billions of users\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const bucketName = \"customers\";\r\n    const imagePath = getCustomerPostImagePath(userId, postId, 0, timestamp, postCreatedAt);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Customer Post Image Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL using admin client\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Error processing customer post image:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Failed to process image. Please try a different image.\"\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgBsB,0BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 6609, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/client-image-compression.ts"], "sourcesContent": ["/**\r\n * Client-side image compression using Canvas API\r\n * This avoids memory issues in serverless environments like Google Cloud Run\r\n */\r\n\r\nexport interface CompressionOptions {\r\n  targetSizeKB?: number;\r\n  maxDimension?: number;\r\n  quality?: number;\r\n  format?: \"webp\" | \"jpeg\" | \"png\";\r\n}\r\n\r\nexport interface CompressionResult {\r\n  blob: Blob;\r\n  finalSizeKB: number;\r\n  compressionRatio: number;\r\n  dimensions: { width: number; height: number };\r\n}\r\n\r\n/**\r\n * Compress image on client-side using Canvas API\r\n */\r\nexport async function compressImageClientSide(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const {\r\n    format = \"webp\",\r\n    targetSizeKB = 100,\r\n    maxDimension = 800,\r\n    quality: initialQuality = 0.8\r\n  } = options;\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      try {\r\n        const canvas = document.createElement('canvas');\r\n        const ctx = canvas.getContext('2d');\r\n        \r\n        if (!ctx) {\r\n          reject(new Error('Could not get canvas context'));\r\n          return;\r\n        }\r\n\r\n        // Calculate new dimensions\r\n        let { width, height } = img;\r\n        \r\n        if (width > maxDimension || height > maxDimension) {\r\n          if (width > height) {\r\n            height = (height * maxDimension) / width;\r\n            width = maxDimension;\r\n          } else {\r\n            width = (width * maxDimension) / height;\r\n            height = maxDimension;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Draw and compress\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n\r\n        // Try different quality levels until we hit target size\r\n        let quality = initialQuality;\r\n        let attempts = 0;\r\n        const maxAttempts = 5;\r\n\r\n        const tryCompress = () => {\r\n          canvas.toBlob((blob) => {\r\n            if (!blob) {\r\n              reject(new Error('Failed to create blob'));\r\n              return;\r\n            }\r\n\r\n            const sizeKB = blob.size / 1024;\r\n            \r\n            if (sizeKB <= targetSizeKB || attempts >= maxAttempts || quality <= 0.1) {\r\n              // Success or max attempts reached\r\n              const compressionRatio = file.size / blob.size;\r\n              resolve({\r\n                blob,\r\n                finalSizeKB: Math.round(sizeKB * 100) / 100,\r\n                compressionRatio: Math.round(compressionRatio * 100) / 100,\r\n                dimensions: { width, height }\r\n              });\r\n            } else {\r\n              // Try again with lower quality\r\n              attempts++;\r\n              quality = Math.max(0.1, quality - 0.15);\r\n              tryCompress();\r\n            }\r\n          }, `image/${format}`, quality);\r\n        };\r\n\r\n        tryCompress();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    };\r\n\r\n    img.onerror = () => reject(new Error('Failed to load image'));\r\n    img.src = URL.createObjectURL(file);\r\n  });\r\n}\r\n\r\n/**\r\n * Ultra-aggressive client-side compression\r\n */\r\nexport async function compressImageUltraAggressiveClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const originalSizeMB = file.size / (1024 * 1024);\r\n  \r\n  // Auto-determine settings based on file size\r\n  let targetSizeKB = 100;\r\n  let maxDimension = 800;\r\n  let quality = 0.7;\r\n\r\n  if (originalSizeMB <= 2) {\r\n    quality = 0.7;\r\n    maxDimension = 800;\r\n    targetSizeKB = 90;\r\n  } else if (originalSizeMB <= 5) {\r\n    quality = 0.55;\r\n    maxDimension = 700;\r\n    targetSizeKB = 80;\r\n  } else if (originalSizeMB <= 10) {\r\n    quality = 0.45;\r\n    maxDimension = 600;\r\n    targetSizeKB = 70;\r\n  } else {\r\n    quality = 0.35;\r\n    maxDimension = 550;\r\n    targetSizeKB = 60;\r\n  }\r\n\r\n  return compressImageClientSide(file, {\r\n    ...options,\r\n    targetSizeKB: options.targetSizeKB || targetSizeKB,\r\n    maxDimension: options.maxDimension || maxDimension,\r\n    quality: options.quality || quality\r\n  });\r\n}\r\n\r\n/**\r\n * Moderate client-side compression\r\n */\r\nexport async function compressImageModerateClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  return compressImageClientSide(file, {\r\n    targetSizeKB: 200,\r\n    maxDimension: 800,\r\n    quality: 0.75,\r\n    ...options\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAmBM,eAAe,wBACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,EACJ,SAAS,MAAM,EACf,eAAe,GAAG,EAClB,eAAe,GAAG,EAClB,SAAS,iBAAiB,GAAG,EAC9B,GAAG;IAEJ,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,IAAI;gBACF,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAE9B,IAAI,CAAC,KAAK;oBACR,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,gBAAgB,SAAS,cAAc;oBACjD,IAAI,QAAQ,QAAQ;wBAClB,SAAS,AAAC,SAAS,eAAgB;wBACnC,QAAQ;oBACV,OAAO;wBACL,QAAQ,AAAC,QAAQ,eAAgB;wBACjC,SAAS;oBACX;gBACF;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;gBAEhC,wDAAwD;gBACxD,IAAI,UAAU;gBACd,IAAI,WAAW;gBACf,MAAM,cAAc;gBAEpB,MAAM,cAAc;oBAClB,OAAO,MAAM,CAAC,CAAC;wBACb,IAAI,CAAC,MAAM;4BACT,OAAO,IAAI,MAAM;4BACjB;wBACF;wBAEA,MAAM,SAAS,KAAK,IAAI,GAAG;wBAE3B,IAAI,UAAU,gBAAgB,YAAY,eAAe,WAAW,KAAK;4BACvE,kCAAkC;4BAClC,MAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI;4BAC9C,QAAQ;gCACN;gCACA,aAAa,KAAK,KAAK,CAAC,SAAS,OAAO;gCACxC,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gCACvD,YAAY;oCAAE;oCAAO;gCAAO;4BAC9B;wBACF,OAAO;4BACL,+BAA+B;4BAC/B;4BACA,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU;4BAClC;wBACF;oBACF,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE;gBACxB;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;QAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;QACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF;AAKO,eAAe,mCACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,iBAAiB,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;IAE/C,6CAA6C;IAC7C,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,UAAU;IAEd,IAAI,kBAAkB,GAAG;QACvB,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,GAAG;QAC9B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,IAAI;QAC/B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO;QACL,UAAU;QACV,eAAe;QACf,eAAe;IACjB;IAEA,OAAO,wBAAwB,MAAM;QACnC,GAAG,OAAO;QACV,cAAc,QAAQ,YAAY,IAAI;QACtC,cAAc,QAAQ,YAAY,IAAI;QACtC,SAAS,QAAQ,OAAO,IAAI;IAC9B;AACF;AAKO,eAAe,4BACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,OAAO,wBAAwB,MAAM;QACnC,cAAc;QACd,cAAc;QACd,SAAS;QACT,GAAG,OAAO;IACZ;AACF", "debugId": null}}, {"offset": {"line": 6731, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/hooks/useCustomerPostMediaUpload.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { uploadCustomerPostImage } from \"@/lib/actions/shared/upload-customer-post-media\";\r\nimport { compressImageUltraAggressiveClient } from \"@/lib/utils/client-image-compression\";\r\n\r\nexport type UploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\nexport interface UseCustomerPostMediaUploadProps {\r\n  onUploadSuccess?: (_url: string) => void;\r\n  onUploadError?: (_error: string) => void;\r\n  maxFileSize?: number; // in bytes, default 15MB\r\n}\r\n\r\n// Allowed file types for posts\r\nconst ALLOWED_FILE_TYPES = [\r\n  'image/jpeg',\r\n  'image/jpg',\r\n  'image/png',\r\n  'image/webp',\r\n  'image/gif'\r\n] as const;\r\n\r\n// Helper function to validate file type\r\nfunction isValidFileType(file: File): boolean {\r\n  return ALLOWED_FILE_TYPES.includes(file.type as typeof ALLOWED_FILE_TYPES[number]);\r\n}\r\n\r\n// Helper function to format file size\r\nfunction formatFileSize(bytes: number): string {\r\n  if (bytes === 0) return '0 Bytes';\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n}\r\n\r\nexport interface UseCustomerPostMediaUploadReturn {\r\n  // State\r\n  uploadStatus: UploadStatus;\r\n  uploadError: string | null;\r\n  previewUrl: string | null;\r\n  isUploading: boolean;\r\n\r\n  // Image cropping\r\n  imageToCrop: string | null;\r\n  originalFile: File | null;\r\n\r\n  // Actions\r\n  handleFileSelect: (_file: File | null) => void;\r\n  handleUpload: (_postId: string, _file?: File, _postCreatedAt?: string) => Promise<string | null>;\r\n  clearImage: () => void;\r\n\r\n  // Cropping actions\r\n  setImageToCrop: (_imageUrl: string | null) => void;\r\n  handleCropComplete: (_croppedFile: File) => void;\r\n  handleCropCancel: () => void;\r\n}\r\n\r\nexport function useCustomerPostMediaUpload({\r\n  onUploadSuccess,\r\n  onUploadError,\r\n  maxFileSize = 15 * 1024 * 1024, // 15MB default - matches industry standards\r\n}: UseCustomerPostMediaUploadProps = {}): UseCustomerPostMediaUploadReturn {\r\n\r\n  const [uploadStatus, setUploadStatus] = useState<UploadStatus>(\"idle\");\r\n  const [uploadError, setUploadError] = useState<string | null>(null);\r\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\r\n  const [imageToCrop, setImageToCrop] = useState<string | null>(null);\r\n  const [originalFile, setOriginalFile] = useState<File | null>(null);\r\n  const [fileToUpload, setFileToUpload] = useState<File | null>(null);\r\n\r\n  // Handle file selection and validation\r\n  const handleFileSelect = useCallback((file: File | null) => {\r\n    // Clear previous state\r\n    setUploadError(null);\r\n    setUploadStatus(\"idle\");\r\n    setPreviewUrl(null);\r\n    setImageToCrop(null);\r\n    setOriginalFile(null);\r\n    setFileToUpload(null);\r\n\r\n    if (!file) return;\r\n\r\n    // Validate file type by MIME type\r\n    if (!isValidFileType(file)) {\r\n      const error = \"Invalid file type. Please upload JPEG, PNG, WebP, or GIF images only.\";\r\n      setUploadError(error);\r\n      setUploadStatus(\"error\");\r\n      toast.error(\"Invalid File Type\", {\r\n        description: \"Only JPEG, PNG, WebP, and GIF images are allowed.\"\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Validate file size with user-friendly message\r\n    if (file.size > maxFileSize) {\r\n      const maxSizeMB = Math.round(maxFileSize / (1024 * 1024));\r\n      const fileSizeFormatted = formatFileSize(file.size);\r\n      const error = `File size (${fileSizeFormatted}) exceeds the ${maxSizeMB}MB limit.`;\r\n      setUploadError(error);\r\n      setUploadStatus(\"error\");\r\n      toast.error(\"File Too Large\", {\r\n        description: `Your file is ${fileSizeFormatted}. Please choose a file smaller than ${maxSizeMB}MB.`\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Store original file directly without cropping\r\n    setOriginalFile(file);\r\n    setFileToUpload(file);\r\n\r\n    // Create preview URL for the original image\r\n    const previewUrl = URL.createObjectURL(file);\r\n    setPreviewUrl(previewUrl);\r\n    setUploadStatus(\"idle\");\r\n  }, [maxFileSize]);\r\n\r\n  // Handle crop completion\r\n  const handleCropComplete = useCallback((croppedFile: File) => {\r\n    setFileToUpload(croppedFile);\r\n    setImageToCrop(null);\r\n\r\n    // Create preview URL for the cropped image\r\n    const previewUrl = URL.createObjectURL(croppedFile);\r\n    setPreviewUrl(previewUrl);\r\n    setUploadStatus(\"idle\");\r\n  }, []);\r\n\r\n  // Handle crop cancellation\r\n  const handleCropCancel = useCallback(() => {\r\n    setImageToCrop(null);\r\n    setOriginalFile(null);\r\n    setUploadStatus(\"idle\");\r\n  }, []);\r\n\r\n  // Handle image upload\r\n  const handleUpload = useCallback(async (\r\n    postId: string,\r\n    file?: File,\r\n    postCreatedAt?: string\r\n  ): Promise<string | null> => {\r\n    const uploadFile = file || fileToUpload;\r\n\r\n    if (!uploadFile) {\r\n      const error = \"No file selected for upload.\";\r\n      setUploadError(error);\r\n      setUploadStatus(\"error\");\r\n      onUploadError?.(error);\r\n      return null;\r\n    }\r\n\r\n    setUploadStatus(\"uploading\");\r\n    setUploadError(null);\r\n\r\n    try {\r\n      // Compress image on client-side first\r\n      const compressionResult = await compressImageUltraAggressiveClient(uploadFile, {\r\n        maxDimension: 1200,\r\n        targetSizeKB: 100\r\n      });\r\n\r\n      // Convert compressed blob to file\r\n      const compressedFile = new File([compressionResult.blob], uploadFile.name, {\r\n        type: compressionResult.blob.type\r\n      });\r\n\r\n      const formData = new FormData();\r\n      formData.append(\"imageFile\", compressedFile);\r\n\r\n      const result = await uploadCustomerPostImage(formData, postId, postCreatedAt);\r\n\r\n      if (result.success && result.url) {\r\n        setUploadStatus(\"success\");\r\n        toast.success(\"Image uploaded successfully!\");\r\n        onUploadSuccess?.(result.url);\r\n        return result.url;\r\n      } else {\r\n        const error = result.error || \"Failed to upload image\";\r\n        setUploadError(error);\r\n        setUploadStatus(\"error\");\r\n        toast.error(\"Upload failed\", {\r\n          description: error\r\n        });\r\n        onUploadError?.(error);\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      const errorMessage = \"An unexpected error occurred during upload\";\r\n      console.error(\"Upload error:\", error);\r\n      setUploadError(errorMessage);\r\n      setUploadStatus(\"error\");\r\n      toast.error(\"Upload failed\", {\r\n        description: errorMessage\r\n      });\r\n      onUploadError?.(errorMessage);\r\n      return null;\r\n    }\r\n  }, [fileToUpload, onUploadSuccess, onUploadError]);\r\n\r\n  // Clear image and reset state\r\n  const clearImage = useCallback(() => {\r\n    setUploadStatus(\"idle\");\r\n    setUploadError(null);\r\n    setFileToUpload(null);\r\n    setOriginalFile(null);\r\n    setImageToCrop(null);\r\n\r\n    if (previewUrl) {\r\n      URL.revokeObjectURL(previewUrl);\r\n      setPreviewUrl(null);\r\n    }\r\n  }, [previewUrl]);\r\n\r\n  return {\r\n    // State\r\n    uploadStatus,\r\n    uploadError,\r\n    previewUrl,\r\n    isUploading: uploadStatus === \"uploading\",\r\n\r\n    // Image cropping\r\n    imageToCrop,\r\n    originalFile,\r\n\r\n    // Actions\r\n    handleFileSelect,\r\n    handleUpload,\r\n    clearImage,\r\n\r\n    // Cropping actions\r\n    setImageToCrop,\r\n    handleCropComplete,\r\n    handleCropCancel,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AAeA,+BAA+B;AAC/B,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;CACD;AAED,wCAAwC;AACxC,SAAS,gBAAgB,IAAU;IACjC,OAAO,mBAAmB,QAAQ,CAAC,KAAK,IAAI;AAC9C;AAEA,sCAAsC;AACtC,SAAS,eAAe,KAAa;IACnC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAwBO,SAAS,2BAA2B,EACzC,eAAe,EACf,aAAa,EACb,cAAc,KAAK,OAAO,IAAI,EACE,GAAG,CAAC,CAAC;;IAErC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,uCAAuC;IACvC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEAAE,CAAC;YACpC,uBAAuB;YACvB,eAAe;YACf,gBAAgB;YAChB,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAEhB,IAAI,CAAC,MAAM;YAEX,kCAAkC;YAClC,IAAI,CAAC,gBAAgB,OAAO;gBAC1B,MAAM,QAAQ;gBACd,eAAe;gBACf,gBAAgB;gBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;oBAC/B,aAAa;gBACf;gBACA;YACF;YAEA,gDAAgD;YAChD,IAAI,KAAK,IAAI,GAAG,aAAa;gBAC3B,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc,CAAC,OAAO,IAAI;gBACvD,MAAM,oBAAoB,eAAe,KAAK,IAAI;gBAClD,MAAM,QAAQ,CAAC,WAAW,EAAE,kBAAkB,cAAc,EAAE,UAAU,SAAS,CAAC;gBAClF,eAAe;gBACf,gBAAgB;gBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kBAAkB;oBAC5B,aAAa,CAAC,aAAa,EAAE,kBAAkB,oCAAoC,EAAE,UAAU,GAAG,CAAC;gBACrG;gBACA;YACF;YAEA,gDAAgD;YAChD,gBAAgB;YAChB,gBAAgB;YAEhB,4CAA4C;YAC5C,MAAM,aAAa,IAAI,eAAe,CAAC;YACvC,cAAc;YACd,gBAAgB;QAClB;mEAAG;QAAC;KAAY;IAEhB,yBAAyB;IACzB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sEAAE,CAAC;YACtC,gBAAgB;YAChB,eAAe;YAEf,2CAA2C;YAC3C,MAAM,aAAa,IAAI,eAAe,CAAC;YACvC,cAAc;YACd,gBAAgB;QAClB;qEAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEAAE;YACnC,eAAe;YACf,gBAAgB;YAChB,gBAAgB;QAClB;mEAAG,EAAE;IAEL,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gEAAE,OAC/B,QACA,MACA;YAEA,MAAM,aAAa,QAAQ;YAE3B,IAAI,CAAC,YAAY;gBACf,MAAM,QAAQ;gBACd,eAAe;gBACf,gBAAgB;gBAChB,gBAAgB;gBAChB,OAAO;YACT;YAEA,gBAAgB;YAChB,eAAe;YAEf,IAAI;gBACF,sCAAsC;gBACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,iJAAA,CAAA,qCAAkC,AAAD,EAAE,YAAY;oBAC7E,cAAc;oBACd,cAAc;gBAChB;gBAEA,kCAAkC;gBAClC,MAAM,iBAAiB,IAAI,KAAK;oBAAC,kBAAkB,IAAI;iBAAC,EAAE,WAAW,IAAI,EAAE;oBACzE,MAAM,kBAAkB,IAAI,CAAC,IAAI;gBACnC;gBAEA,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,aAAa;gBAE7B,MAAM,SAAS,MAAM,CAAA,GAAA,mKAAA,CAAA,0BAAuB,AAAD,EAAE,UAAU,QAAQ;gBAE/D,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;oBAChC,gBAAgB;oBAChB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,kBAAkB,OAAO,GAAG;oBAC5B,OAAO,OAAO,GAAG;gBACnB,OAAO;oBACL,MAAM,QAAQ,OAAO,KAAK,IAAI;oBAC9B,eAAe;oBACf,gBAAgB;oBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;wBAC3B,aAAa;oBACf;oBACA,gBAAgB;oBAChB,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe;gBACrB,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,eAAe;gBACf,gBAAgB;gBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;oBAC3B,aAAa;gBACf;gBACA,gBAAgB;gBAChB,OAAO;YACT;QACF;+DAAG;QAAC;QAAc;QAAiB;KAAc;IAEjD,8BAA8B;IAC9B,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YAC7B,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YAEf,IAAI,YAAY;gBACd,IAAI,eAAe,CAAC;gBACpB,cAAc;YAChB;QACF;6DAAG;QAAC;KAAW;IAEf,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA,aAAa,iBAAiB;QAE9B,iBAAiB;QACjB;QACA;QAEA,UAAU;QACV;QACA;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;IACF;AACF;GAhLgB", "debugId": null}}, {"offset": {"line": 6945, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 7021, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/forms/CustomerPostForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { toast } from 'sonner';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Send, Plus, Loader2 } from 'lucide-react';\r\nimport Image from 'next/image';\r\nimport { createCustomerPost, updateCustomerPost } from '@/lib/actions/customerPosts';\r\nimport { customerPostSchema, CustomerPostFormData, CustomerPostData } from '@/lib/types/posts';\r\nimport LocationDisplay from './LocationDisplay';\r\nimport MediaUpload from './MediaUpload';\r\nimport { useCustomerPostMediaUpload } from '../hooks/useCustomerPostMediaUpload';\r\nimport { createClient } from '@/utils/supabase/client';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\n\r\ninterface CustomerPostFormProps {\r\n  post?: CustomerPostData;\r\n  onSuccess?: () => void;\r\n  onCancel?: () => void;\r\n  showCard?: boolean;\r\n}\r\n\r\nexport default function CustomerPostForm({ post, onSuccess, onCancel, showCard = true }: CustomerPostFormProps) {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [uploadMethod, setUploadMethod] = useState<'upload' | 'url'>('upload');\r\n  const router = useRouter();\r\n\r\n  // Initialize form with existing post data or defaults\r\n  const form = useForm<CustomerPostFormData>({\r\n    resolver: zodResolver(customerPostSchema),\r\n    defaultValues: {\r\n      id: post?.id,\r\n      content: post?.content || '',\r\n      image_url: post?.image_url || null,\r\n      mentioned_business_ids: post?.mentioned_business_ids || [],\r\n    },\r\n  });\r\n\r\n  // Media upload hook\r\n  const {\r\n    uploadError,\r\n    previewUrl,\r\n    isUploading,\r\n    imageToCrop: _imageToCrop,\r\n    originalFile: _originalFile,\r\n    handleFileSelect,\r\n    handleUpload,\r\n    clearImage,\r\n    handleCropComplete: _handleCropComplete,\r\n    handleCropCancel: _handleCropCancel,\r\n  } = useCustomerPostMediaUpload({\r\n    onUploadSuccess: (url) => {\r\n      form.setValue('image_url', url, { shouldDirty: true });\r\n    },\r\n    onUploadError: (error) => {\r\n      toast.error('Upload failed', { description: error });\r\n    },\r\n  });\r\n\r\n  // Helper function to clear all images\r\n  const handleClearAllImages = () => {\r\n    clearImage();\r\n    form.setValue('image_url', null, { shouldDirty: true });\r\n    setUploadMethod('upload');\r\n  };\r\n\r\n  // Check if customer has complete profile (name and address)\r\n  const checkCustomerProfile = async () => {\r\n    const supabase = createClient();\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n\r\n    if (!user) {\r\n      toast.error('Please log in to continue');\r\n      return false;\r\n    }\r\n\r\n    const { data: profile, error } = await supabase\r\n      .from('customer_profiles')\r\n      .select('name, pincode, city, state, locality')\r\n      .eq('id', user.id)\r\n      .single();\r\n\r\n    if (error) {\r\n      toast.error('Failed to check customer profile');\r\n      return false;\r\n    }\r\n\r\n    // Check if name is filled\r\n    if (!profile?.name || profile.name.trim() === '') {\r\n      toast.error('Please complete your name in your profile before creating posts', {\r\n        description: 'You will be redirected to update your profile',\r\n        action: {\r\n          label: 'Update Now',\r\n          onClick: () => router.push('/dashboard/customer/profile')\r\n        }\r\n      });\r\n\r\n      // Redirect after a short delay\r\n      setTimeout(() => {\r\n        router.push('/dashboard/customer/profile');\r\n      }, 2000);\r\n\r\n      return false;\r\n    }\r\n\r\n    // Check if all address fields are filled\r\n    if (!profile?.pincode || !profile?.city || !profile?.state || !profile?.locality) {\r\n      toast.error('Please complete your address in your profile before creating posts', {\r\n        description: 'You will be redirected to update your profile',\r\n        action: {\r\n          label: 'Update Now',\r\n          onClick: () => router.push('/dashboard/customer/profile')\r\n        }\r\n      });\r\n\r\n      // Redirect after a short delay\r\n      setTimeout(() => {\r\n        router.push('/dashboard/customer/profile');\r\n      }, 2000);\r\n\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  // Handle form submission\r\n  const onSubmit = async (data: CustomerPostFormData) => {\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      // Check customer profile (name and address) before creating new posts\r\n      if (!post?.id) {\r\n        const hasValidProfile = await checkCustomerProfile();\r\n        if (!hasValidProfile) {\r\n          setIsSubmitting(false);\r\n          return;\r\n        }\r\n      }\r\n      const finalData = { ...data };\r\n\r\n      let result;\r\n\r\n      if (post?.id) {\r\n        // Updating existing post\r\n        result = await updateCustomerPost(post.id, finalData);\r\n      } else {\r\n        // Creating new post\r\n        if (previewUrl && !finalData.image_url) {\r\n          // Handle file upload for new posts\r\n          const createResult = await createCustomerPost({\r\n            ...finalData,\r\n            image_url: null // Create post first without image\r\n          });\r\n\r\n          if (createResult.success && createResult.data) {\r\n            const postData = createResult.data as CustomerPostData;\r\n            const uploadUrl = await handleUpload(postData.id, undefined, postData.created_at);\r\n            if (uploadUrl) {\r\n              // Update post with image URL\r\n              const updateResult = await updateCustomerPost(postData.id, {\r\n                ...finalData,\r\n                image_url: uploadUrl\r\n              });\r\n              result = updateResult;\r\n            } else {\r\n              result = createResult; // Post created but image upload failed\r\n            }\r\n          } else {\r\n            result = createResult;\r\n          }\r\n        } else {\r\n          // No file upload needed or URL provided\r\n          result = await createCustomerPost(finalData);\r\n        }\r\n      }\r\n\r\n      if (result.success) {\r\n        toast.success(result.message);\r\n        form.reset();\r\n        clearImage(); // Clear media upload state\r\n        setUploadMethod('upload'); // Reset to upload method\r\n        if (onSuccess) {\r\n          onSuccess();\r\n        }\r\n      } else {\r\n        toast.error(result.error || result.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error submitting post:', error);\r\n      toast.error('An unexpected error occurred');\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const formContent = (\r\n    <Form {...form}>\r\n      <form onSubmit={form.handleSubmit(onSubmit)}>\r\n        <div className=\"space-y-4\">\r\n          {/* Location Display */}\r\n          <LocationDisplay className=\"mb-4\" />\r\n\r\n          {/* Post content */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"content\"\r\n            render={({ field }) => {\r\n              const charCount = field.value?.length || 0;\r\n              const maxChars = 2000;\r\n              const isOverLimit = charCount > maxChars;\r\n\r\n              return (\r\n                <FormItem>\r\n                  <FormLabel>Content</FormLabel>\r\n                  <FormControl>\r\n                    <div className=\"relative\">\r\n                      <Textarea\r\n                        placeholder=\"What's on your mind?\"\r\n                        className={`min-h-[120px] pr-16 ${isOverLimit ? 'border-destructive focus:border-destructive' : ''}`}\r\n                        {...field}\r\n                      />\r\n                      <div className={`absolute bottom-2 right-2 text-xs ${\r\n                        isOverLimit ? 'text-destructive' : 'text-muted-foreground'\r\n                      }`}>\r\n                        {charCount}/{maxChars}\r\n                      </div>\r\n                    </div>\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              );\r\n            }}\r\n          />\r\n\r\n          {/* Image Upload/URL Section */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"image_url\"\r\n            render={({ field }) => (\r\n              <FormItem>\r\n                <FormLabel>Add Image (optional)</FormLabel>\r\n\r\n                {/* Upload method tabs */}\r\n                <Tabs value={uploadMethod} onValueChange={(value) => setUploadMethod(value as 'upload' | 'url')}>\r\n                  <TabsList className=\"grid w-full grid-cols-2\">\r\n                    <TabsTrigger value=\"upload\">Upload Image</TabsTrigger>\r\n                    <TabsTrigger value=\"url\">Image URL</TabsTrigger>\r\n                  </TabsList>\r\n\r\n                  <TabsContent value=\"upload\" className=\"space-y-4\">\r\n                    <MediaUpload\r\n                      previewUrl={previewUrl || (uploadMethod === 'upload' ? (field.value || null) : null)}\r\n                      isUploading={isUploading}\r\n                      uploadError={uploadError}\r\n                      onFileSelect={handleFileSelect}\r\n                      onClearImage={handleClearAllImages}\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </TabsContent>\r\n\r\n                  <TabsContent value=\"url\" className=\"space-y-4\">\r\n                    <FormControl>\r\n                      <div className=\"space-y-2\">\r\n                        <input\r\n                          type=\"url\"\r\n                          placeholder=\"Enter image URL (https://...)\"\r\n                          value={uploadMethod === 'url' ? (field.value || '') : ''}\r\n                          onChange={(e) => {\r\n                            if (uploadMethod === 'url') {\r\n                              field.onChange(e.target.value || null);\r\n                            }\r\n                          }}\r\n                          disabled={isSubmitting}\r\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:border-gray-600 dark:bg-gray-800 dark:text-white\"\r\n                        />\r\n                        {uploadMethod === 'url' && field.value && (\r\n                          <div className=\"relative\">\r\n                            <Image\r\n                              src={field.value}\r\n                              alt=\"Preview\"\r\n                              width={400}\r\n                              height={300}\r\n                              className=\"w-full max-w-sm h-auto rounded-lg border border-gray-200 dark:border-gray-700\"\r\n                              onError={() => {\r\n                                toast.error('Invalid image URL');\r\n                                field.onChange(null);\r\n                              }}\r\n                            />\r\n                            <button\r\n                              type=\"button\"\r\n                              onClick={() => field.onChange(null)}\r\n                              className=\"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\"\r\n                              disabled={isSubmitting}\r\n                            >\r\n                              ×\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </FormControl>\r\n                  </TabsContent>\r\n                </Tabs>\r\n\r\n                <FormMessage />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex flex-col-reverse sm:flex-row gap-3 pt-6\">\r\n          {onCancel && (\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              onClick={onCancel}\r\n              disabled={isSubmitting}\r\n              className=\"flex-1 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200\"\r\n            >\r\n              Cancel\r\n            </Button>\r\n          )}\r\n\r\n          <motion.div\r\n            whileTap={{ scale: 0.98 }}\r\n            className=\"flex-1\"\r\n          >\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className={`\r\n                w-full relative overflow-hidden\r\n                bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-dark)]\r\n                hover:from-[var(--brand-gold-dark)] hover:to-[var(--brand-gold)]\r\n                text-[var(--brand-gold-foreground)] font-medium\r\n                shadow-lg hover:shadow-xl\r\n                transition-all duration-300\r\n                before:absolute before:inset-0\r\n                before:bg-gradient-to-r before:from-[var(--brand-gold)]/80 before:to-[var(--brand-gold-dark)]/80\r\n                before:opacity-0 hover:before:opacity-20\r\n                before:transition-opacity before:duration-300\r\n                ${isSubmitting ? 'cursor-not-allowed opacity-80' : ''}\r\n              `}\r\n            >\r\n              <AnimatePresence mode=\"wait\">\r\n                {isSubmitting ? (\r\n                  <motion.div\r\n                    key=\"submitting\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 10 }}\r\n                    className=\"flex items-center justify-center\"\r\n                  >\r\n                    <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                    {post?.id ? 'Updating...' : 'Posting...'}\r\n                  </motion.div>\r\n                ) : (\r\n                  <motion.div\r\n                    key=\"submit\"\r\n                    initial={{ opacity: 0, x: -10 }}\r\n                    animate={{ opacity: 1, x: 0 }}\r\n                    exit={{ opacity: 0, x: 10 }}\r\n                    className=\"flex items-center justify-center\"\r\n                  >\r\n                    {post?.id ? (\r\n                      <>\r\n                        <Send className=\"h-4 w-4 mr-2\" />\r\n                        Update Post\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <Plus className=\"h-4 w-4 mr-2\" />\r\n                        Create Post\r\n                      </>\r\n                    )}\r\n                  </motion.div>\r\n                )}\r\n              </AnimatePresence>\r\n            </Button>\r\n          </motion.div>\r\n        </div>\r\n      </form>\r\n    </Form>\r\n  );\r\n\r\n  if (!showCard) {\r\n    return formContent;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Card className=\"w-full\">\r\n        <CardHeader>\r\n          <CardTitle>{post?.id ? 'Edit Post' : 'Create New Post'}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          {formContent}\r\n        </CardContent>\r\n      </Card>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;;;;;;;;;;;AA6Be,SAAS,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAyB;;IAC5G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,sDAAsD;IACtD,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAwB;QACzC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,wHAAA,CAAA,qBAAkB;QACxC,eAAe;YACb,IAAI,MAAM;YACV,SAAS,MAAM,WAAW;YAC1B,WAAW,MAAM,aAAa;YAC9B,wBAAwB,MAAM,0BAA0B,EAAE;QAC5D;IACF;IAEA,oBAAoB;IACpB,MAAM,EACJ,WAAW,EACX,UAAU,EACV,WAAW,EACX,aAAa,YAAY,EACzB,cAAc,aAAa,EAC3B,gBAAgB,EAChB,YAAY,EACZ,UAAU,EACV,oBAAoB,mBAAmB,EACvC,kBAAkB,iBAAiB,EACpC,GAAG,CAAA,GAAA,sKAAA,CAAA,6BAA0B,AAAD,EAAE;QAC7B,eAAe;2DAAE,CAAC;gBAChB,KAAK,QAAQ,CAAC,aAAa,KAAK;oBAAE,aAAa;gBAAK;YACtD;;QACA,aAAa;2DAAE,CAAC;gBACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;oBAAE,aAAa;gBAAM;YACpD;;IACF;IAEA,sCAAsC;IACtC,MAAM,uBAAuB;QAC3B;QACA,KAAK,QAAQ,CAAC,aAAa,MAAM;YAAE,aAAa;QAAK;QACrD,gBAAgB;IAClB;IAEA,4DAA4D;IAC5D,MAAM,uBAAuB;QAC3B,MAAM,WAAW,CAAA,GAAA,8HAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAEtD,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,wCACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,OAAO;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,0BAA0B;QAC1B,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI;YAChD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mEAAmE;gBAC7E,aAAa;gBACb,QAAQ;oBACN,OAAO;oBACP,SAAS,IAAM,OAAO,IAAI,CAAC;gBAC7B;YACF;YAEA,+BAA+B;YAC/B,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;YAEH,OAAO;QACT;QAEA,yCAAyC;QACzC,IAAI,CAAC,SAAS,WAAW,CAAC,SAAS,QAAQ,CAAC,SAAS,SAAS,CAAC,SAAS,UAAU;YAChF,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,sEAAsE;gBAChF,aAAa;gBACb,QAAQ;oBACN,OAAO;oBACP,SAAS,IAAM,OAAO,IAAI,CAAC;gBAC7B;YACF;YAEA,+BAA+B;YAC/B,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;YAEH,OAAO;QACT;QAEA,OAAO;IACT;IAEA,yBAAyB;IACzB,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAEhB,IAAI;YACF,sEAAsE;YACtE,IAAI,CAAC,MAAM,IAAI;gBACb,MAAM,kBAAkB,MAAM;gBAC9B,IAAI,CAAC,iBAAiB;oBACpB,gBAAgB;oBAChB;gBACF;YACF;YACA,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAE5B,IAAI;YAEJ,IAAI,MAAM,IAAI;gBACZ,yBAAyB;gBACzB,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,EAAE,EAAE;YAC7C,OAAO;gBACL,oBAAoB;gBACpB,IAAI,cAAc,CAAC,UAAU,SAAS,EAAE;oBACtC,mCAAmC;oBACnC,MAAM,eAAe,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE;wBAC5C,GAAG,SAAS;wBACZ,WAAW,KAAK,kCAAkC;oBACpD;oBAEA,IAAI,aAAa,OAAO,IAAI,aAAa,IAAI,EAAE;wBAC7C,MAAM,WAAW,aAAa,IAAI;wBAClC,MAAM,YAAY,MAAM,aAAa,SAAS,EAAE,EAAE,WAAW,SAAS,UAAU;wBAChF,IAAI,WAAW;4BACb,6BAA6B;4BAC7B,MAAM,eAAe,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,EAAE,EAAE;gCACzD,GAAG,SAAS;gCACZ,WAAW;4BACb;4BACA,SAAS;wBACX,OAAO;4BACL,SAAS,cAAc,uCAAuC;wBAChE;oBACF,OAAO;wBACL,SAAS;oBACX;gBACF,OAAO;oBACL,wCAAwC;oBACxC,SAAS,MAAM,CAAA,GAAA,0IAAA,CAAA,qBAAkB,AAAD,EAAE;gBACpC;YACF;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;gBAC5B,KAAK,KAAK;gBACV,cAAc,2BAA2B;gBACzC,gBAAgB,WAAW,yBAAyB;gBACpD,IAAI,WAAW;oBACb;gBACF;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,4BACJ,6LAAC,4HAAA,CAAA,OAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YAAK,UAAU,KAAK,YAAY,CAAC;;8BAChC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,4JAAA,CAAA,UAAe;4BAAC,WAAU;;;;;;sCAG3B,6LAAC,4HAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE;gCAChB,MAAM,YAAY,MAAM,KAAK,EAAE,UAAU;gCACzC,MAAM,WAAW;gCACjB,MAAM,cAAc,YAAY;gCAEhC,qBACE,6LAAC,4HAAA,CAAA,WAAQ;;sDACP,6LAAC,4HAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,4HAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gIAAA,CAAA,WAAQ;wDACP,aAAY;wDACZ,WAAW,CAAC,oBAAoB,EAAE,cAAc,gDAAgD,IAAI;wDACnG,GAAG,KAAK;;;;;;kEAEX,6LAAC;wDAAI,WAAW,CAAC,kCAAkC,EACjD,cAAc,qBAAqB,yBACnC;;4DACC;4DAAU;4DAAE;;;;;;;;;;;;;;;;;;sDAInB,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;4BAGlB;;;;;;sCAIF,6LAAC,4HAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;;sDACP,6LAAC,4HAAA,CAAA,YAAS;sDAAC;;;;;;sDAGX,6LAAC,4HAAA,CAAA,OAAI;4CAAC,OAAO;4CAAc,eAAe,CAAC,QAAU,gBAAgB;;8DACnE,6LAAC,4HAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,6LAAC,4HAAA,CAAA,cAAW;4DAAC,OAAM;sEAAS;;;;;;sEAC5B,6LAAC,4HAAA,CAAA,cAAW;4DAAC,OAAM;sEAAM;;;;;;;;;;;;8DAG3B,6LAAC,4HAAA,CAAA,cAAW;oDAAC,OAAM;oDAAS,WAAU;8DACpC,cAAA,6LAAC,wJAAA,CAAA,UAAW;wDACV,YAAY,cAAc,CAAC,iBAAiB,WAAY,MAAM,KAAK,IAAI,OAAQ,IAAI;wDACnF,aAAa;wDACb,aAAa;wDACb,cAAc;wDACd,cAAc;wDACd,UAAU;;;;;;;;;;;8DAId,6LAAC,4HAAA,CAAA,cAAW;oDAAC,OAAM;oDAAM,WAAU;8DACjC,cAAA,6LAAC,4HAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,MAAK;oEACL,aAAY;oEACZ,OAAO,iBAAiB,QAAS,MAAM,KAAK,IAAI,KAAM;oEACtD,UAAU,CAAC;wEACT,IAAI,iBAAiB,OAAO;4EAC1B,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI;wEACnC;oEACF;oEACA,UAAU;oEACV,WAAU;;;;;;gEAEX,iBAAiB,SAAS,MAAM,KAAK,kBACpC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,gIAAA,CAAA,UAAK;4EACJ,KAAK,MAAM,KAAK;4EAChB,KAAI;4EACJ,OAAO;4EACP,QAAQ;4EACR,WAAU;4EACV,SAAS;gFACP,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gFACZ,MAAM,QAAQ,CAAC;4EACjB;;;;;;sFAEF,6LAAC;4EACC,MAAK;4EACL,SAAS,IAAM,MAAM,QAAQ,CAAC;4EAC9B,WAAU;4EACV,UAAU;sFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAUb,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;8BAMpB,6LAAC;oBAAI,WAAU;;wBACZ,0BACC,6LAAC,8HAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAKH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC;;;;;;;;;;;gBAWV,EAAE,eAAe,kCAAkC,GAAG;cACxD,CAAC;0CAED,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,MAAK;8CACnB,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,WAAU;;0DAEV,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,MAAM,KAAK,gBAAgB;;uCAPxB;;;;6DAUN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,WAAU;kDAET,MAAM,mBACL;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;yEAInC;;8DACE,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;uCAbjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BtB,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,qBACE;kBACE,cAAA,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,4HAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;kCAAE,MAAM,KAAK,cAAc;;;;;;;;;;;8BAEvC,6LAAC,4HAAA,CAAA,cAAW;8BACT;;;;;;;;;;;;;AAKX;GA3XwB;;QAGP,qIAAA,CAAA,YAAS;QAGX,iKAAA,CAAA,UAAO;QAsBhB,sKAAA,CAAA,6BAA0B;;;KA5BR", "debugId": null}}, {"offset": {"line": 7662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/shared/ModernCustomerPostCreator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Plus, X } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport CustomerPostForm from '@/components/feed/shared/forms/CustomerPostForm';\r\nimport { useCustomerProfile } from '@/contexts/UserDataContext';\r\n\r\ninterface ModernCustomerPostCreatorProps {\r\n  customerName?: string;\r\n  onPostCreated?: () => void;\r\n}\r\n\r\nexport default function ModernCustomerPostCreator({\r\n  customerName = 'Customer',\r\n  onPostCreated\r\n}: ModernCustomerPostCreatorProps) {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const { customerProfile, getCustomerProfile } = useCustomerProfile();\r\n\r\n  // Use profile data from context or fallback to prop\r\n  const customerDisplayName = customerProfile?.name || customerName || 'Customer';\r\n  const customerAvatar = customerProfile?.avatar_url;\r\n\r\n  // Fetch customer profile data if not already available\r\n  useEffect(() => {\r\n    if (!customerProfile) {\r\n      getCustomerProfile();\r\n    }\r\n  }, [customerProfile, getCustomerProfile]);\r\n\r\n  const handlePostSuccess = () => {\r\n    setIsExpanded(false);\r\n    if (onPostCreated) {\r\n      onPostCreated();\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setIsExpanded(false);\r\n  };\r\n\r\n  return (\r\n    <Card className=\"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 shadow-sm overflow-hidden\">\r\n      <CardContent className=\"p-0\">\r\n        <AnimatePresence mode=\"wait\">\r\n          {!isExpanded ? (\r\n            <motion.div\r\n              key=\"collapsed\"\r\n              initial={{ opacity: 0, height: 0 }}\r\n              animate={{ opacity: 1, height: 'auto' }}\r\n              exit={{ opacity: 0, height: 0 }}\r\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n              className=\"px-4 py-1 md:px-4 md:py-1.5\"\r\n            >\r\n              {/* Simplified Layout: Avatar + Text + Plus Button (matching React Native) */}\r\n              <div className=\"flex items-center gap-3 cursor-pointer\" onClick={() => setIsExpanded(true)}>\r\n                <Avatar className=\"w-12 h-12 border-2 border-[var(--brand-gold)]/30 shadow-sm\">\r\n                  <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />\r\n                  <AvatarFallback className=\"bg-muted text-foreground border border-[var(--brand-gold)]/30\">\r\n                    {customerDisplayName.charAt(0).toUpperCase()}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n\r\n                <div className=\"flex-1\">\r\n                  <p className=\"text-neutral-500 dark:text-neutral-400 font-normal text-base\">\r\n                    What&apos;s on your mind, {customerDisplayName}?\r\n                  </p>\r\n                </div>\r\n\r\n                <Button\r\n                  size=\"sm\"\r\n                  className=\"h-8 w-8 p-0 bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-dark)] text-white rounded-full\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    setIsExpanded(true);\r\n                  }}\r\n                >\r\n                  <Plus className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n\r\n\r\n            </motion.div>\r\n          ) : (\r\n            <motion.div\r\n              key=\"expanded\"\r\n              initial={{ opacity: 0, height: 0 }}\r\n              animate={{ opacity: 1, height: 'auto' }}\r\n              exit={{ opacity: 0, height: 0 }}\r\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n              className=\"overflow-hidden\"\r\n            >\r\n              {/* Header - Simplified like React Native */}\r\n              <div className=\"flex items-center justify-between p-4 md:p-6 pb-4 border-b border-neutral-200 dark:border-neutral-700\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={handleCancel}\r\n                  className=\"h-8 w-8 p-0 hover:bg-neutral-100 dark:hover:bg-neutral-800\"\r\n                >\r\n                  <X className=\"h-4 w-4\" />\r\n                </Button>\r\n\r\n                <h3 className=\"font-semibold text-lg text-neutral-900 dark:text-neutral-100\">\r\n                  Create Post\r\n                </h3>\r\n\r\n                <div className=\"w-8 h-8\" /> {/* Spacer for centering */}\r\n              </div>\r\n\r\n              {/* Content */}\r\n              <div className=\"p-4 md:p-6 pt-4\">\r\n                {/* User Info - matching React Native */}\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <Avatar className=\"w-12 h-12 border-2 border-[var(--brand-gold)]/30 shadow-sm\">\r\n                    <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />\r\n                    <AvatarFallback className=\"bg-muted text-foreground border border-[var(--brand-gold)]/30\">\r\n                      {customerDisplayName.charAt(0).toUpperCase()}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  <div>\r\n                    <h4 className=\"font-semibold text-base text-neutral-900 dark:text-neutral-100\">\r\n                      {customerDisplayName}\r\n                    </h4>\r\n                  </div>\r\n                </div>\r\n\r\n                <CustomerPostForm\r\n                  onSuccess={handlePostSuccess}\r\n                  onCancel={handleCancel}\r\n                  showCard={false}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAgBe,SAAS,0BAA0B,EAChD,eAAe,UAAU,EACzB,aAAa,EACkB;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD;IAEjE,oDAAoD;IACpD,MAAM,sBAAsB,iBAAiB,QAAQ,gBAAgB;IACrE,MAAM,iBAAiB,iBAAiB;IAExC,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB;YACF;QACF;8CAAG;QAAC;QAAiB;KAAmB;IAExC,MAAM,oBAAoB;QACxB,cAAc;QACd,IAAI,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,cAAc;IAChB;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;8BAGV,cAAA,6LAAC;wBAAI,WAAU;wBAAyC,SAAS,IAAM,cAAc;;0CACnF,6LAAC,8HAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,8HAAA,CAAA,cAAW;wCAAC,KAAK,kBAAkB;wCAAW,KAAK;;;;;;kDACpD,6LAAC,8HAAA,CAAA,iBAAc;wCAAC,WAAU;kDACvB,oBAAoB,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;0CAI9C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAA+D;wCAC/C;wCAAoB;;;;;;;;;;;;0CAInD,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,cAAc;gCAChB;0CAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;mBA9BhB;;;;yCAqCN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;8CAGf,6LAAC;oCAAG,WAAU;8CAA+D;;;;;;8CAI7E,6LAAC;oCAAI,WAAU;;;;;;gCAAY;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,8HAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,8HAAA,CAAA,cAAW;oDAAC,KAAK,kBAAkB;oDAAW,KAAK;;;;;;8DACpD,6LAAC,8HAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,oBAAoB,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;sDAG9C,6LAAC;sDACC,cAAA,6LAAC;gDAAG,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAKP,6LAAC,6JAAA,CAAA,UAAgB;oCACf,WAAW;oCACX,UAAU;oCACV,UAAU;;;;;;;;;;;;;mBA7CV;;;;;;;;;;;;;;;;;;;;AAsDlB;GA/HwB;;QAK0B,+HAAA,CAAA,qBAAkB;;;KAL5C", "debugId": null}}, {"offset": {"line": 7981, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/feed/ModernCustomerFeedList.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useInView } from 'react-intersection-observer';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Loader2, AlertCircle } from 'lucide-react';\r\nimport { getUnifiedFeedPostsWithAuthors, UnifiedPost } from '@/lib/actions/posts/unifiedFeed';\r\nimport { FeedFilterType } from '@/lib/types/posts';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\nimport UnifiedPostCard from './shared/UnifiedPostCard';\r\nimport PostCardSkeleton from './shared/PostCardSkeleton';\r\nimport ModernFeedHeader from './shared/ModernFeedHeader';\r\nimport ModernFeedContainer from './shared/ModernFeedContainer';\r\nimport ModernCustomerPostCreator from './shared/ModernCustomerPostCreator';\r\n\r\ninterface ModernCustomerFeedListProps {\r\n  initialPosts: UnifiedPost[];\r\n  initialTotalCount: number;\r\n  initialHasMore: boolean;\r\n  initialFilter?: FeedFilterType;\r\n  citySlug?: string;\r\n  stateSlug?: string;\r\n  localitySlug?: string;\r\n  pincode?: string;\r\n  userName?: string;\r\n}\r\n\r\nexport default function ModernCustomerFeedList({\r\n  initialPosts,\r\n  initialTotalCount,\r\n  initialHasMore,\r\n  initialFilter = 'smart',\r\n  citySlug,\r\n  stateSlug,\r\n  localitySlug,\r\n  pincode,\r\n  userName = 'Valued Customer'\r\n}: ModernCustomerFeedListProps) {\r\n  // State for posts and pagination\r\n  const [posts, setPosts] = useState<UnifiedPost[]>(initialPosts);\r\n  const [_totalCount, setTotalCount] = useState<number>(initialTotalCount);\r\n  const [hasMore, setHasMore] = useState<boolean>(initialHasMore);\r\n  const [page, setPage] = useState<number>(1);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n  const [filter, setFilter] = useState<FeedFilterType>(initialFilter);\r\n\r\n  // Intersection observer for infinite scroll\r\n  const { ref, inView } = useInView();\r\n\r\n  // Load more posts\r\n  const loadMorePosts = useCallback(async () => {\r\n    if (!hasMore || isLoading) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const nextPage = page + 1;\r\n      const result = await getUnifiedFeedPostsWithAuthors({\r\n        filter,\r\n        page: nextPage,\r\n        city_slug: citySlug,\r\n        state_slug: stateSlug,\r\n        locality_slug: localitySlug,\r\n        pincode: pincode,\r\n      });\r\n\r\n      if (result.success && result.data?.items) {\r\n        setPosts(prev => {\r\n          // Create a Set of existing post IDs for fast lookup\r\n          const existingIds = new Set(prev.map(post => post.id));\r\n          // Filter out any posts that already exist\r\n          const newPosts = result.data!.items.filter(post => !existingIds.has(post.id));\r\n          return [...prev, ...newPosts];\r\n        });\r\n        setHasMore(result.data!.hasMore || false);\r\n        setTotalCount(result.data!.totalCount || 0);\r\n        setPage(nextPage);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading more posts:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [hasMore, isLoading, page, filter, citySlug, stateSlug, localitySlug, pincode]);\r\n\r\n  // Load more posts when the user scrolls to the bottom\r\n  useEffect(() => {\r\n    if (inView && hasMore && !isLoading) {\r\n      loadMorePosts();\r\n    }\r\n  }, [inView, hasMore, isLoading, loadMorePosts]);\r\n\r\n  // Handle filter change\r\n  const handleFilterChange = async (newFilter: FeedFilterType) => {\r\n    if (newFilter === filter) return;\r\n\r\n    setIsLoading(true);\r\n    setFilter(newFilter);\r\n\r\n    // Clear existing posts immediately to show skeletons\r\n    setPosts([]);\r\n\r\n    try {\r\n      const result = await getUnifiedFeedPostsWithAuthors({\r\n        filter: newFilter,\r\n        page: 1,\r\n        city_slug: citySlug,\r\n        state_slug: stateSlug,\r\n        locality_slug: localitySlug,\r\n        pincode: pincode,\r\n      });\r\n\r\n      if (result.success && result.data) {\r\n        setPosts(result.data.items);\r\n        setHasMore(result.data.hasMore);\r\n        setTotalCount(result.data.totalCount);\r\n        setPage(1);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error changing filter:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle post creation success\r\n  const handlePostCreated = async () => {\r\n    // Refresh the feed\r\n    try {\r\n      const result = await getUnifiedFeedPostsWithAuthors({\r\n        filter,\r\n        page: 1,\r\n        city_slug: citySlug,\r\n        state_slug: stateSlug,\r\n        locality_slug: localitySlug,\r\n        pincode: pincode,\r\n      });\r\n\r\n      if (result.success && result.data?.items) {\r\n        // For refresh, we replace all posts so no deduplication needed\r\n        setPosts(result.data.items);\r\n        setHasMore(result.data.hasMore || false);\r\n        setTotalCount(result.data.totalCount || 0);\r\n        setPage(1);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error refreshing feed:', error);\r\n    }\r\n  };\r\n\r\n  // Handle post update\r\n  const handlePostUpdate = (postId: string, newContent: string) => {\r\n    setPosts(prevPosts =>\r\n      prevPosts.map(post =>\r\n        post.id === postId\r\n          ? { ...post, content: newContent }\r\n          : post\r\n      )\r\n    );\r\n  };\r\n\r\n  // Handle post deletion\r\n  const handlePostDelete = (postId: string) => {\r\n    setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));\r\n    setTotalCount(prevCount => Math.max(0, prevCount - 1));\r\n  };\r\n\r\n  // Handle product update\r\n  const handleProductsUpdate = (postId: string, newProductIds: string[]) => {\r\n    setPosts(prevPosts =>\r\n      prevPosts.map(post =>\r\n        post.id === postId\r\n          ? { ...post, product_ids: newProductIds }\r\n          : post\r\n      )\r\n    );\r\n  };\r\n\r\n  // Get empty state message\r\n  const getEmptyStateMessage = () => {\r\n    switch (filter) {\r\n      case 'smart':\r\n        return 'No posts available in your smart feed. Try subscribing to businesses or check other filters.';\r\n      case 'subscribed':\r\n        return 'Subscribe to businesses to see their posts here.';\r\n      case 'locality':\r\n        return 'No posts from businesses in your locality yet.';\r\n      case 'pincode':\r\n        return 'No posts from businesses in your pincode yet.';\r\n      case 'city':\r\n        return 'No posts from businesses in your city yet.';\r\n      case 'state':\r\n        return 'No posts from businesses in your state yet.';\r\n      default:\r\n        return 'No posts available at the moment.';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ModernFeedContainer>\r\n      <ModernFeedHeader\r\n        activeFilter={filter}\r\n        onFilterChange={handleFilterChange}\r\n        isLoading={isLoading}\r\n      />\r\n\r\n      {/* Posts Content */}\r\n      <div className=\"max-w-2xl mx-auto space-y-6\">\r\n        {/* Customer Post Creation Card */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.2 }}\r\n        >\r\n          <ModernCustomerPostCreator\r\n            customerName={userName}\r\n            onPostCreated={handlePostCreated}\r\n          />\r\n        </motion.div>\r\n\r\n        <AnimatePresence mode=\"wait\">\r\n          {posts.length === 0 && !isLoading ? (\r\n            <motion.div\r\n              key=\"empty-state\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <Alert className=\"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800\">\r\n                <AlertCircle className=\"h-4 w-4\" />\r\n                <AlertTitle>No posts found</AlertTitle>\r\n                <AlertDescription>{getEmptyStateMessage()}</AlertDescription>\r\n              </Alert>\r\n            </motion.div>\r\n          ) : (\r\n            <motion.div\r\n              key=\"posts-list\"\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              transition={{ duration: 0.3 }}\r\n              className=\"space-y-0\"\r\n            >\r\n              {/* Loading skeletons during filter change */}\r\n              {isLoading && posts.length === 0 && (\r\n                <>\r\n                  {Array.from({ length: 10 }).map((_, index) => (\r\n                    <PostCardSkeleton\r\n                      key={`skeleton-${index}`}\r\n                      index={index}\r\n                      showImage={Math.random() > 0.3}\r\n                      showProducts={Math.random() > 0.7}\r\n                    />\r\n                  ))}\r\n                </>\r\n              )}\r\n\r\n              {/* Posts */}\r\n              {posts.map((post, index) => (\r\n                <UnifiedPostCard\r\n                  key={post.id}\r\n                  post={post}\r\n                  index={index}\r\n                  onPostUpdate={handlePostUpdate}\r\n                  onPostDelete={handlePostDelete}\r\n                  onProductsUpdate={handleProductsUpdate}\r\n                />\r\n              ))}\r\n\r\n              {/* Infinite scroll trigger */}\r\n              {hasMore && (\r\n                <div ref={ref} className=\"flex justify-center items-center py-8\">\r\n                  {isLoading && (\r\n                    <div className=\"flex items-center gap-2 text-neutral-500\">\r\n                      <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                      <span className=\"text-sm\">Loading more posts...</span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Load more button (fallback) */}\r\n              {hasMore && !isLoading && (\r\n                <div className=\"flex justify-center mt-8 mb-4\">\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    onClick={loadMorePosts}\r\n                    disabled={isLoading}\r\n                    className=\"bg-white dark:bg-black border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-900\"\r\n                  >\r\n                    Load More Posts\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </ModernFeedContainer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AA4Be,SAAS,uBAAuB,EAC7C,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,gBAAgB,OAAO,EACvB,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,OAAO,EACP,WAAW,iBAAiB,EACA;;IAC5B,iCAAiC;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErD,4CAA4C;IAC5C,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD;IAEhC,kBAAkB;IAClB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAChC,IAAI,CAAC,WAAW,WAAW;YAE3B,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,OAAO;gBACxB,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,iCAA8B,AAAD,EAAE;oBAClD;oBACA,MAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,eAAe;oBACf,SAAS;gBACX;gBAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,OAAO;oBACxC;6EAAS,CAAA;4BACP,oDAAoD;4BACpD,MAAM,cAAc,IAAI,IAAI,KAAK,GAAG;qFAAC,CAAA,OAAQ,KAAK,EAAE;;4BACpD,0CAA0C;4BAC1C,MAAM,WAAW,OAAO,IAAI,CAAE,KAAK,CAAC,MAAM;8FAAC,CAAA,OAAQ,CAAC,YAAY,GAAG,CAAC,KAAK,EAAE;;4BAC3E,OAAO;mCAAI;mCAAS;6BAAS;wBAC/B;;oBACA,WAAW,OAAO,IAAI,CAAE,OAAO,IAAI;oBACnC,cAAc,OAAO,IAAI,CAAE,UAAU,IAAI;oBACzC,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,SAAU;gBACR,aAAa;YACf;QACF;4DAAG;QAAC;QAAS;QAAW;QAAM;QAAQ;QAAU;QAAW;QAAc;KAAQ;IAEjF,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,UAAU,WAAW,CAAC,WAAW;gBACnC;YACF;QACF;2CAAG;QAAC;QAAQ;QAAS;QAAW;KAAc;IAE9C,uBAAuB;IACvB,MAAM,qBAAqB,OAAO;QAChC,IAAI,cAAc,QAAQ;QAE1B,aAAa;QACb,UAAU;QAEV,qDAAqD;QACrD,SAAS,EAAE;QAEX,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAClD,QAAQ;gBACR,MAAM;gBACN,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;YACX;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,SAAS,OAAO,IAAI,CAAC,KAAK;gBAC1B,WAAW,OAAO,IAAI,CAAC,OAAO;gBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;gBACpC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,yIAAA,CAAA,iCAA8B,AAAD,EAAE;gBAClD;gBACA,MAAM;gBACN,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;YACX;YAEA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE,OAAO;gBACxC,+DAA+D;gBAC/D,SAAS,OAAO,IAAI,CAAC,KAAK;gBAC1B,WAAW,OAAO,IAAI,CAAC,OAAO,IAAI;gBAClC,cAAc,OAAO,IAAI,CAAC,UAAU,IAAI;gBACxC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC,QAAgB;QACxC,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAW,IAC/B;IAGV;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,CAAC;QACxB,SAAS,CAAA,YAAa,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC3D,cAAc,CAAA,YAAa,KAAK,GAAG,CAAC,GAAG,YAAY;IACrD;IAEA,wBAAwB;IACxB,MAAM,uBAAuB,CAAC,QAAgB;QAC5C,SAAS,CAAA,YACP,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAc,IACtC;IAGV;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,uJAAA,CAAA,UAAmB;;0BAClB,6LAAC,oJAAA,CAAA,UAAgB;gBACf,cAAc;gBACd,gBAAgB;gBAChB,WAAW;;;;;;0BAIb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,6JAAA,CAAA,UAAyB;4BACxB,cAAc;4BACd,eAAe;;;;;;;;;;;kCAInB,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,MAAM,MAAM,KAAK,KAAK,CAAC,0BACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC,6HAAA,CAAA,aAAU;kDAAC;;;;;;kDACZ,6LAAC,6HAAA,CAAA,mBAAgB;kDAAE;;;;;;;;;;;;2BATjB;;;;iDAaN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;gCAGT,aAAa,MAAM,MAAM,KAAK,mBAC7B;8CACG,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClC,6LAAC,oJAAA,CAAA,UAAgB;4CAEf,OAAO;4CACP,WAAW,KAAK,MAAM,KAAK;4CAC3B,cAAc,KAAK,MAAM,KAAK;2CAHzB,CAAC,SAAS,EAAE,OAAO;;;;;;gCAU/B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,mJAAA,CAAA,UAAe;wCAEd,MAAM;wCACN,OAAO;wCACP,cAAc;wCACd,cAAc;wCACd,kBAAkB;uCALb,KAAK,EAAE;;;;;gCAUf,yBACC,6LAAC;oCAAI,KAAK;oCAAK,WAAU;8CACtB,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;gCAOjC,WAAW,CAAC,2BACX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;2BArDD;;;;;;;;;;;;;;;;;;;;;;AAgElB;GAlRwB;;QAoBE,sKAAA,CAAA,YAAS;;;KApBX", "debugId": null}}]}