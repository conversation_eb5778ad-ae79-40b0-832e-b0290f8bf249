{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/reviews/ReviewSortDropdown.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { SortAsc } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\nexport type ReviewSortOption =\r\n  | \"newest\"\r\n  | \"oldest\"\r\n  | \"highest_rating\"\r\n  | \"lowest_rating\";\r\n\r\ninterface ReviewSortDropdownProps {\r\n  sortBy: ReviewSortOption;\r\n  onSortChange: (_sortBy: ReviewSortOption) => void;\r\n  className?: string;\r\n}\r\n\r\nconst sortOptions = [\r\n  { value: \"newest\" as const, label: \"Newest First\" },\r\n  { value: \"oldest\" as const, label: \"Oldest First\" },\r\n  { value: \"highest_rating\" as const, label: \"High to Low Ratings\" },\r\n  { value: \"lowest_rating\" as const, label: \"Low to High Ratings\" },\r\n];\r\n\r\nexport default function ReviewSortDropdown({\r\n  sortBy,\r\n  onSortChange,\r\n  className = \"\",\r\n}: ReviewSortDropdownProps) {\r\n  const currentSortLabel = sortOptions.find(option => option.value === sortBy)?.label || \"Newest First\";\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4, delay: 0.2 }}\r\n      className={className}\r\n    >\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            className=\"gap-1.5 bg-white dark:bg-black border-neutral-200 dark:border-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200\"\r\n          >\r\n            <SortAsc className=\"h-4 w-4\" />\r\n            <span>Sort: </span>\r\n            <span className=\"font-medium text-[var(--brand-gold)]\">\r\n              {currentSortLabel}\r\n            </span>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\" className=\"w-48 p-1\">\r\n          {sortOptions.map((option) => (\r\n            <DropdownMenuItem\r\n              key={option.value}\r\n              onClick={() => onSortChange(option.value)}\r\n              className={`cursor-pointer transition-colors duration-200 ${\r\n                sortBy === option.value\r\n                  ? \"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] font-medium\"\r\n                  : \"hover:bg-neutral-100 dark:hover:bg-neutral-800\"\r\n              }`}\r\n            >\r\n              {option.label}\r\n            </DropdownMenuItem>\r\n          ))}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAyBA,MAAM,cAAc;IAClB;QAAE,OAAO;QAAmB,OAAO;IAAe;IAClD;QAAE,OAAO;QAAmB,OAAO;IAAe;IAClD;QAAE,OAAO;QAA2B,OAAO;IAAsB;IACjE;QAAE,OAAO;QAA0B,OAAO;IAAsB;CACjE;AAEc,SAAS,mBAAmB,EACzC,MAAM,EACN,YAAY,EACZ,YAAY,EAAE,EACU;IACxB,MAAM,mBAAmB,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,SAAS,SAAS;IAEvF,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,WAAW;kBAEX,cAAA,6LAAC,wIAAA,CAAA,eAAY;;8BACX,6LAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,6LAAC,iOAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;;8BAIP,6LAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAM,WAAU;8BACxC,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC,wIAAA,CAAA,mBAAgB;4BAEf,SAAS,IAAM,aAAa,OAAO,KAAK;4BACxC,WAAW,CAAC,8CAA8C,EACxD,WAAW,OAAO,KAAK,GACnB,mEACA,kDACJ;sCAED,OAAO,KAAK;2BARR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;AAe/B;KA9CwB", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { buttonVariants } from \"@/components/ui/button\";\r\n\r\n// Define ButtonProps type based on the button component's props\r\ntype ButtonProps = React.ComponentProps<\"button\"> & {\r\n  size?: \"default\" | \"sm\" | \"lg\" | \"icon\";\r\n};\r\n\r\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\r\n  <nav\r\n    role=\"navigation\"\r\n    aria-label=\"pagination\"\r\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n    {...props}\r\n  />\r\n);\r\n\r\nconst PaginationContent = React.forwardRef<\r\n  HTMLUListElement,\r\n  React.ComponentProps<\"ul\">\r\n>(({ className, ...props }, ref) => (\r\n  <ul\r\n    ref={ref}\r\n    className={cn(\"flex flex-row items-center gap-1\", className)}\r\n    {...props}\r\n  />\r\n));\r\nPaginationContent.displayName = \"PaginationContent\";\r\n\r\nconst PaginationItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.ComponentProps<\"li\">\r\n>(({ className, ...props }, ref) => (\r\n  <li ref={ref} className={cn(\"\", className)} {...props} />\r\n));\r\nPaginationItem.displayName = \"PaginationItem\";\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean;\r\n} & Pick<ButtonProps, \"size\"> &\r\n  React.ComponentProps<\"a\">;\r\n\r\nconst PaginationLink = ({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) => (\r\n  <a\r\n    aria-current={isActive ? \"page\" : undefined}\r\n    className={cn(\r\n      buttonVariants({\r\n        variant: isActive ? \"outline\" : \"ghost\",\r\n        size,\r\n      }),\r\n      className,\r\n      isActive && \"bg-muted hover:bg-muted pointer-events-none\"\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nPaginationLink.displayName = \"PaginationLink\";\r\n\r\nconst PaginationPrevious = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to previous page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pl-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <ChevronLeft className=\"h-4 w-4\" />\r\n    <span>Previous</span>\r\n  </PaginationLink>\r\n);\r\nPaginationPrevious.displayName = \"PaginationPrevious\";\r\n\r\nconst PaginationNext = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) => (\r\n  <PaginationLink\r\n    aria-label=\"Go to next page\"\r\n    size=\"default\"\r\n    className={cn(\"gap-1 pr-2.5\", className)}\r\n    {...props}\r\n  >\r\n    <span>Next</span>\r\n    <ChevronRight className=\"h-4 w-4\" />\r\n  </PaginationLink>\r\n);\r\nPaginationNext.displayName = \"PaginationNext\";\r\n\r\nconst PaginationEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) => (\r\n  <span\r\n    aria-hidden\r\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"h-4 w-4\" />\r\n    <span className=\"sr-only\">More pages</span>\r\n  </span>\r\n);\r\nPaginationEllipsis.displayName = \"PaginationEllipsis\";\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;AACA;AANA;;;;;;AAaA,MAAM,aAAa,CAAC,EAAE,SAAS,EAAE,GAAG,OAAoC,iBACtE,6LAAC;QACC,MAAK;QACL,cAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;KALP;AASN,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAG,KAAK;QAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;;;;;;;AAEvD,eAAe,WAAW,GAAG;AAO7B,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB,iBACpB,6LAAC;QACC,gBAAc,WAAW,SAAS;QAClC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,8HAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA,WACA,YAAY;QAEb,GAAG,KAAK;;;;;;MAhBP;AAmBN,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACyC,iBAC5C,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,6LAAC;0BAAK;;;;;;;;;;;;MAXJ;AAcN,mBAAmB,WAAW,GAAG;AAEjC,MAAM,iBAAiB,CAAC,EACtB,SAAS,EACT,GAAG,OACyC,iBAC5C,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB;QAC7B,GAAG,KAAK;;0BAET,6LAAC;0BAAK;;;;;;0BACN,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;MAXtB;AAcN,eAAe,WAAW,GAAG;AAE7B,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,6LAAC;QACC,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;MAVxB;AAaN,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForSubscribe = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForSubscribe\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use admin client to bypass RLS\r\n    const supabaseAdminForUnsubscribe = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnsubscribe\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use admin client to bypass RLS\r\n    const supabaseAdminForReview = createAdminClient();\r\n    const { error: upsertError } = await supabaseAdminForReview\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForDeleteReview = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForDeleteReview\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdmin = createAdminClient();\r\n    const { data: cardData } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use admin client to bypass RLS\r\n    const supabaseAdminForLike = createAdminClient();\r\n    const { error: insertError } = await supabaseAdminForLike\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForSlug = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForSlug\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use admin client to bypass RLS\r\n    const supabaseAdminForUnlike = createAdminClient();\r\n    const { error: deleteError } = await supabaseAdminForUnlike\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForCardData = createAdminClient();\r\n    const { data: cardData } = await supabaseAdminForCardData\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use admin client to bypass RLS\r\n    const supabaseAdminForStatus = createAdminClient();\r\n\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabaseAdminForStatus\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabaseAdminForStatus\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6PsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/reviews/ReviewCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Star, Edit, Trash2, Loader2, ExternalLink, Check, X, User, Building2 } from \"lucide-react\";\r\nimport { deleteReview } from \"@/lib/actions/interactions\";\r\nimport { toast } from \"sonner\";\r\nimport { formatDistanceToNow } from \"date-fns\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\n\r\ninterface BusinessProfileDataForReview {\r\n  id: string;\r\n  business_name: string | null;\r\n  business_slug: string | null;\r\n  logo_url: string | null;\r\n}\r\n\r\ninterface ReviewData {\r\n  id: string;\r\n  rating: number;\r\n  review_text: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  business_profiles: BusinessProfileDataForReview | null;\r\n  reviewer_type?: 'business' | 'customer';\r\n  reviewer_slug?: string | null;\r\n}\r\n\r\ninterface ReviewCardProps {\r\n  review: ReviewData;\r\n  onDeleteSuccess: ((_reviewId: string) => void) | null;\r\n  isReviewsReceivedTab?: boolean;\r\n}\r\n\r\nexport default function ReviewCard({\r\n  review,\r\n  onDeleteSuccess,\r\n  isReviewsReceivedTab = false,\r\n}: ReviewCardProps) {\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [editedRating, setEditedRating] = useState(review.rating);\r\n  const [editedReviewText, setEditedReviewText] = useState(review.review_text || \"\");\r\n\r\n  // Access business_profiles directly from the review object\r\n  const business = review.business_profiles;\r\n\r\n  // Handle potential issues with updated_at\r\n  let timeAgo;\r\n  try {\r\n    timeAgo = formatDistanceToNow(new Date(review.updated_at || review.created_at), { addSuffix: true });\r\n  } catch (_error) {\r\n    timeAgo = 'recently';\r\n  }\r\n\r\n  // Handle delete\r\n  const handleDelete = async () => {\r\n    if (!review.business_profile_id) {\r\n      toast.error('Cannot delete review: Missing business information');\r\n      return;\r\n    }\r\n\r\n    const confirmation = confirm(\"Are you sure you want to delete this review?\");\r\n    if (!confirmation) return;\r\n\r\n    try {\r\n      setIsDeleting(true);\r\n      const result = await deleteReview(review.business_profile_id);\r\n\r\n      if (result.success) {\r\n        toast.success(`Review for ${business?.business_name || 'business'} deleted.`);\r\n        if (onDeleteSuccess) {\r\n          onDeleteSuccess(review.id);\r\n        }\r\n      } else {\r\n        toast.error(`Failed to delete review: ${result.error || 'Unknown error'}`);\r\n      }\r\n    } catch (_error) {\r\n      toast.error('An error occurred while deleting the review');\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Handle edit mode toggle\r\n  const handleEdit = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle cancel edit\r\n  const handleCancelEdit = () => {\r\n    setIsEditing(false);\r\n    setEditedRating(review.rating);\r\n    setEditedReviewText(review.review_text || \"\");\r\n  };\r\n\r\n  // Handle save edit\r\n  const handleSaveEdit = async () => {\r\n    setIsUpdating(true);\r\n    try {\r\n      const response = await fetch(\"/api/customer/reviews/update\", {\r\n        method: \"PATCH\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          reviewId: review.id,\r\n          rating: editedRating,\r\n          reviewText: editedReviewText,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        toast.success(\"Review updated successfully\");\r\n        setIsEditing(false);\r\n        // Update the local review data\r\n        review.rating = editedRating;\r\n        review.review_text = editedReviewText;\r\n      } else {\r\n        toast.error(data.error || \"Failed to update review\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating review:\", error);\r\n      toast.error(\"An unexpected error occurred\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4 }}\r\n      whileHover={!isEditing ? { y: -5, transition: { duration: 0.2 } } : {}}\r\n      className={cn(\r\n        \"rounded-xl border border-neutral-200 dark:border-neutral-800\",\r\n        \"bg-white dark:bg-black\", // Changed to black in dark mode\r\n        \"shadow-sm p-4 transition-all duration-300 hover:shadow-md\",\r\n        \"relative overflow-hidden group\",\r\n        isEditing && \"ring-2 ring-amber-400 dark:ring-amber-600\"\r\n      )}\r\n    >\r\n      {/* Card background with subtle pattern */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n          backgroundRepeat: \"no-repeat\",\r\n        }}\r\n      ></div>\r\n\r\n      <div className=\"relative z-10\">\r\n        {/* Business info and rating */}\r\n        <div className=\"flex items-start mb-3\">\r\n          <div className=\"flex items-center gap-3\">\r\n            {/* Make avatar clickable if it's a business reviewer */}\r\n            {review.reviewer_type === 'business' && review.reviewer_slug ? (\r\n              <Link\r\n                href={`/${review.reviewer_slug}`}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"hover:opacity-80 transition-opacity\"\r\n              >\r\n                <Avatar className=\"h-10 w-10 border border-neutral-200 dark:border-neutral-800\">\r\n                  {business?.logo_url ? (\r\n                    <AvatarImage\r\n                      src={business.logo_url}\r\n                      alt={business?.business_name || \"Business\"}\r\n                    />\r\n                  ) : null}\r\n                  <AvatarFallback className=\"bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300\">\r\n                    {business?.business_name?.[0]?.toUpperCase() || \"B\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n              </Link>\r\n            ) : (\r\n              <Avatar className=\"h-10 w-10 border border-neutral-200 dark:border-neutral-800\">\r\n                {business?.logo_url ? (\r\n                  <AvatarImage\r\n                    src={business.logo_url}\r\n                    alt={business?.business_name || \"Business\"}\r\n                  />\r\n                ) : null}\r\n                <AvatarFallback className=\"bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300\">\r\n                  {business?.business_name?.[0]?.toUpperCase() || \"B\"}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n            )}\r\n\r\n            <div className=\"flex-1\">\r\n              {/* Business name with redirect icon */}\r\n              <div className=\"flex items-center gap-1\">\r\n                {review.reviewer_type === 'business' && review.reviewer_slug ? (\r\n                  <Link\r\n                    href={`/${review.reviewer_slug}`}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1\"\r\n                  >\r\n                    <h3 className=\"font-medium text-neutral-800 dark:text-neutral-200\">\r\n                      {business?.business_name || \"Unknown Business\"}\r\n                    </h3>\r\n                    <ExternalLink className=\"h-3.5 w-3.5 opacity-70\" />\r\n                  </Link>\r\n                ) : (\r\n                  <h3 className=\"font-medium text-neutral-800 dark:text-neutral-200\">\r\n                    {business?.business_name || \"Unknown User\"}\r\n                  </h3>\r\n                )}\r\n              </div>\r\n\r\n              {/* Reviewer type badge - only show in Reviews Received tab */}\r\n              {isReviewsReceivedTab && (\r\n                <div className=\"flex items-center gap-1 mt-1\">\r\n                  {review.reviewer_type === 'business' ? (\r\n                    <div className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium\">\r\n                      <Building2 className=\"h-3 w-3\" />\r\n                      Business\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium\">\r\n                      <User className=\"h-3 w-3\" />\r\n                      Customer\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Rating stars - only show in non-editing mode */}\r\n              {!isEditing && (\r\n                <div className=\"flex items-center gap-1 mt-1\">\r\n                  {Array.from({ length: 5 }).map((_, i) => (\r\n                    <Star\r\n                      key={i}\r\n                      className={cn(\r\n                        \"h-4 w-4\",\r\n                        i < review.rating\r\n                          ? \"text-amber-500 fill-amber-500\"\r\n                          : \"text-neutral-300 dark:text-neutral-700\"\r\n                      )}\r\n                    />\r\n                  ))}\r\n                  <span className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                    {timeAgo}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Review content */}\r\n        {isEditing ? (\r\n          // Edit mode content\r\n          <div className=\"space-y-3\">\r\n            {/* Star rating selector */}\r\n            <div className=\"flex items-center gap-1\">\r\n              {Array.from({ length: 5 }).map((_, i) => (\r\n                <button\r\n                  key={i}\r\n                  type=\"button\"\r\n                  onClick={() => setEditedRating(i + 1)}\r\n                  className=\"focus:outline-none\"\r\n                >\r\n                  <Star\r\n                    className={cn(\r\n                      \"h-6 w-6 transition-colors\",\r\n                      i < editedRating\r\n                        ? \"text-amber-500 fill-amber-500\"\r\n                        : \"text-neutral-300 dark:text-neutral-700 hover:text-amber-400\"\r\n                    )}\r\n                  />\r\n                </button>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Review text input */}\r\n            <Textarea\r\n              value={editedReviewText}\r\n              onChange={(e) => setEditedReviewText(e.target.value)}\r\n              placeholder=\"What did you like or dislike? What stood out about your experience?\"\r\n              className=\"w-full bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-amber-400 focus:border-amber-400 transition-all duration-200 resize-none\"\r\n              maxLength={500}\r\n              rows={4}\r\n            />\r\n            <div className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-1 text-right\">\r\n              {editedReviewText.length}/500\r\n            </div>\r\n\r\n            {/* Edit mode buttons */}\r\n            <div className=\"flex items-center justify-end gap-2 mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"text-xs h-8 gap-1\"\r\n                onClick={handleCancelEdit}\r\n              >\r\n                <X className=\"h-3.5 w-3.5\" />\r\n                Cancel\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"default\"\r\n                size=\"sm\"\r\n                className=\"text-xs h-8 gap-1 bg-amber-500 hover:bg-amber-600 text-white\"\r\n                onClick={handleSaveEdit}\r\n                disabled={isUpdating}\r\n              >\r\n                {isUpdating ? (\r\n                  <Loader2 className=\"h-3.5 w-3.5 animate-spin\" />\r\n                ) : (\r\n                  <Check className=\"h-3.5 w-3.5\" />\r\n                )}\r\n                Save\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Display mode content */}\r\n            {review.review_text && (\r\n              <div className=\"mb-4\">\r\n                <p className=\"text-neutral-700 dark:text-neutral-300 text-sm leading-relaxed\">\r\n                  {review.review_text}\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            {/* Action buttons - only show in \"My Reviews\" tab */}\r\n            {!isReviewsReceivedTab && (\r\n              <div className=\"flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50\">\r\n                {/* View Business button - only show for business reviews */}\r\n                {review.reviewer_type === 'business' && business?.business_slug ? (\r\n                  <Button\r\n                    asChild\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"text-xs h-8 gap-1\"\r\n                  >\r\n                    <Link\r\n                      href={`/${business.business_slug}`}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                    >\r\n                      <ExternalLink className=\"h-3.5 w-3.5\" />\r\n                      View Business\r\n                    </Link>\r\n                  </Button>\r\n                ) : (\r\n                  <div></div> // Empty div to maintain layout when no View Business button\r\n                )}\r\n\r\n                {/* Only show edit/delete buttons if onDeleteSuccess is provided */}\r\n                {onDeleteSuccess && (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"text-xs h-8 gap-1 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-900 dark:hover:bg-blue-950/30\"\r\n                      onClick={handleEdit}\r\n                    >\r\n                      <Edit className=\"h-3.5 w-3.5\" />\r\n                      Edit\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"text-xs h-8 gap-1 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-900 dark:hover:bg-red-950/30\"\r\n                      onClick={handleDelete}\r\n                      disabled={isDeleting}\r\n                    >\r\n                      {isDeleting ? (\r\n                        <Loader2 className=\"h-3.5 w-3.5 animate-spin\" />\r\n                      ) : (\r\n                        <Trash2 className=\"h-3.5 w-3.5\" />\r\n                      )}\r\n                      Delete\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAwCe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,EACf,uBAAuB,KAAK,EACZ;;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,MAAM;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,WAAW,IAAI;IAE/E,2DAA2D;IAC3D,MAAM,WAAW,OAAO,iBAAiB;IAEzC,0CAA0C;IAC1C,IAAI;IACJ,IAAI;QACF,UAAU,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG;YAAE,WAAW;QAAK;IACpG,EAAE,OAAO,QAAQ;QACf,UAAU;IACZ;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,mBAAmB,EAAE;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe,QAAQ;QAC7B,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,cAAc;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,mBAAmB;YAE5D,IAAI,OAAO,OAAO,EAAE;gBAClB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,UAAU,iBAAiB,WAAW,SAAS,CAAC;gBAC5E,IAAI,iBAAiB;oBACnB,gBAAgB,OAAO,EAAE;gBAC3B;YACF,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,KAAK,IAAI,iBAAiB;YAC3E;QACF,EAAE,OAAO,QAAQ;YACf,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,aAAa;QACb,gBAAgB,OAAO,MAAM;QAC7B,oBAAoB,OAAO,WAAW,IAAI;IAC5C;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,OAAO,EAAE;oBACnB,QAAQ;oBACR,YAAY;gBACd;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,aAAa;gBACb,+BAA+B;gBAC/B,OAAO,MAAM,GAAG;gBAChB,OAAO,WAAW,GAAG;YACvB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY,CAAC,YAAY;YAAE,GAAG,CAAC;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE,IAAI,CAAC;QACrE,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gEACA,0BACA,6DACA,kCACA,aAAa;;0BAIf,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,mCAAmC,CAAC;oBACtD,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCAEZ,OAAO,aAAa,KAAK,cAAc,OAAO,aAAa,iBAC1D,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,EAAE;oCAChC,QAAO;oCACP,KAAI;oCACJ,WAAU;8CAEV,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCAAC,WAAU;;4CACf,UAAU,yBACT,6LAAC,8HAAA,CAAA,cAAW;gDACV,KAAK,SAAS,QAAQ;gDACtB,KAAK,UAAU,iBAAiB;;;;;uDAEhC;0DACJ,6LAAC,8HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,UAAU,eAAe,CAAC,EAAE,EAAE,iBAAiB;;;;;;;;;;;;;;;;yDAKtD,6LAAC,8HAAA,CAAA,SAAM;oCAAC,WAAU;;wCACf,UAAU,yBACT,6LAAC,8HAAA,CAAA,cAAW;4CACV,KAAK,SAAS,QAAQ;4CACtB,KAAK,UAAU,iBAAiB;;;;;mDAEhC;sDACJ,6LAAC,8HAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,UAAU,eAAe,CAAC,EAAE,EAAE,iBAAiB;;;;;;;;;;;;8CAKtD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,OAAO,aAAa,KAAK,cAAc,OAAO,aAAa,iBAC1D,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,EAAE;gDAChC,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEACX,UAAU,iBAAiB;;;;;;kEAE9B,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;qEAG1B,6LAAC;gDAAG,WAAU;0DACX,UAAU,iBAAiB;;;;;;;;;;;wCAMjC,sCACC,6LAAC;4CAAI,WAAU;sDACZ,OAAO,aAAa,KAAK,2BACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAY;;;;;;qEAInC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;wCAQnC,CAAC,2BACA,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ;gDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,qMAAA,CAAA,OAAI;wDAEH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,OAAO,MAAM,GACb,kCACA;uDALD;;;;;8DAST,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASZ,YACC,oBAAoB;kCACpB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,gBAAgB,IAAI;wCACnC,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CACH,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6BACA,IAAI,eACA,kCACA;;;;;;uCAVH;;;;;;;;;;0CAkBX,6LAAC,gIAAA,CAAA,WAAQ;gCACP,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,aAAY;gCACZ,WAAU;gCACV,WAAW;gCACX,MAAM;;;;;;0CAER,6LAAC;gCAAI,WAAU;;oCACZ,iBAAiB,MAAM;oCAAC;;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;;0DAET,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;kDAI/B,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;wCACT,UAAU;;4CAET,2BACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CACjB;;;;;;;;;;;;;;;;;;6CAMR;;4BAEG,OAAO,WAAW,kBACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CACV,OAAO,WAAW;;;;;;;;;;;4BAMxB,CAAC,sCACA,6LAAC;gCAAI,WAAU;;oCAEZ,OAAO,aAAa,KAAK,cAAc,UAAU,8BAChD,6LAAC,8HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;4CAClC,QAAO;4CACP,KAAI;;8DAEJ,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAgB;;;;;;;;;;;6DAK5C,6LAAC;;;;6CAAW,4DAA4D;;oCAIzE,iCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;;kEAET,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;0DAIlC,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;;oDAET,2BACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB;GAxWwB;KAAA", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/reviews/ReviewCardSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface ReviewCardSkeletonProps {\r\n  index?: number;\r\n}\r\n\r\nexport default function ReviewCardSkeleton({ index = 0 }: ReviewCardSkeletonProps) {\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4, delay: index * 0.05 }}\r\n      className={cn(\r\n        \"rounded-xl border border-neutral-200 dark:border-neutral-800\",\r\n        \"bg-white dark:bg-black\",\r\n        \"shadow-sm p-4 transition-all duration-300\",\r\n        \"relative overflow-hidden\"\r\n      )}\r\n    >\r\n      {/* Card background with subtle pattern */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n          backgroundRepeat: \"no-repeat\",\r\n        }}\r\n      ></div>\r\n\r\n      <div className=\"relative z-10\">\r\n        {/* Business info and rating skeleton */}\r\n        <div className=\"flex items-start mb-3\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <Skeleton className=\"h-10 w-10 rounded-full\" />\r\n            <div>\r\n              <Skeleton className=\"h-5 w-32 mb-2\" />\r\n              {/* Rating stars */}\r\n              <div className=\"flex items-center gap-1 mt-1\">\r\n                {Array.from({ length: 5 }).map((_, i) => (\r\n                  <Skeleton key={i} className=\"h-4 w-4\" />\r\n                ))}\r\n              </div>\r\n              {/* Date below ratings */}\r\n              <Skeleton className=\"h-3 w-24 mt-1\" />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Review text skeleton */}\r\n        <div className=\"mb-4\">\r\n          <Skeleton className=\"h-20 w-full rounded-lg\" />\r\n        </div>\r\n\r\n        {/* Action buttons skeleton */}\r\n        <div className=\"flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50\">\r\n          <Skeleton className=\"h-8 w-28\" />\r\n          <div className=\"flex items-center gap-2\">\r\n            <Skeleton className=\"h-8 w-16\" />\r\n            <Skeleton className=\"h-8 w-20\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,mBAAmB,EAAE,QAAQ,CAAC,EAA2B;IAC/E,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;QACjD,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gEACA,0BACA,6CACA;;0BAIF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,mCAAmC,CAAC;oBACtD,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;;sDACC,6LAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;sDACZ,MAAM,IAAI,CAAC;gDAAE,QAAQ;4CAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,gIAAA,CAAA,WAAQ;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAInB,6LAAC,gIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;KA3DwB", "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/components/EnhancedReviewListClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { AlertCircle } from \"lucide-react\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\n\r\nimport ReviewSortDropdown, { ReviewSortOption } from \"@/app/components/shared/reviews/ReviewSortDropdown\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport ReviewCard from \"@/app/components/shared/reviews/ReviewCard\";\r\nimport ReviewCardSkeleton from \"@/app/components/shared/reviews/ReviewCardSkeleton\";\r\n\r\ninterface BusinessProfileDataForReview {\r\n  id: string;\r\n  business_name: string | null;\r\n  business_slug: string | null;\r\n  logo_url: string | null;\r\n}\r\n\r\ninterface ReviewData {\r\n  id: string;\r\n  rating: number;\r\n  review_text: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  business_profiles: BusinessProfileDataForReview | null;\r\n}\r\n\r\ninterface PaginationData {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  perPage: number;\r\n}\r\n\r\nexport default function EnhancedReviewListClient() {\r\n  const [reviews, setReviews] = useState<ReviewData[]>([]);\r\n  const [sortBy, setSortBy] = useState<ReviewSortOption>(\"newest\");\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [pagination, setPagination] = useState<PaginationData>({\r\n    currentPage: 1,\r\n    totalPages: 1,\r\n    totalCount: 0,\r\n    perPage: 10\r\n  });\r\n\r\n\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  // Fetch reviews from the API\r\n  const fetchReviews = useCallback(async (page: number, sort: ReviewSortOption) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const queryParams = new URLSearchParams({\r\n        page: page.toString(),\r\n        sort: sort,\r\n      });\r\n\r\n      const response = await fetch(`/api/customer/reviews?${queryParams.toString()}`);\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch reviews');\r\n      }\r\n\r\n      const data = await response.json();\r\n      setReviews(data.reviews);\r\n      setPagination(data.pagination);\r\n    } catch (_err) {\r\n      setError('Failed to load reviews. Please try again.');\r\n      setReviews([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Fetch reviews when page or sort changes\r\n  useEffect(() => {\r\n    fetchReviews(pagination.currentPage, sortBy);\r\n  }, [pagination.currentPage, sortBy, fetchReviews]);\r\n\r\n  // Handle page change\r\n  const handlePageChange = (page: number) => {\r\n    setPagination(prev => ({ ...prev, currentPage: page }));\r\n  };\r\n\r\n  // Handle review deletion\r\n  const handleDeleteSuccess = (reviewId: string) => {\r\n    setReviews(prevReviews => prevReviews.filter(r => r.id !== reviewId));\r\n\r\n    // If we deleted the last review on the page, go to previous page\r\n    if (reviews.length === 1 && pagination.currentPage > 1) {\r\n      handlePageChange(pagination.currentPage - 1);\r\n    } else {\r\n      // Refresh the current page\r\n      fetchReviews(pagination.currentPage, sortBy);\r\n    }\r\n  };\r\n\r\n  // Handle sort change\r\n  const handleSortChange = (newSortBy: ReviewSortOption) => {\r\n    setSortBy(newSortBy);\r\n    // Reset to first page when sorting changes\r\n    setPagination(prev => ({ ...prev, currentPage: 1 }));\r\n  };\r\n\r\n\r\n\r\n  // Generate pagination items\r\n  const generatePaginationItems = () => {\r\n    const items = [];\r\n    const { currentPage, totalPages } = pagination;\r\n\r\n    // Always show first page\r\n    items.push(\r\n      <PaginationItem key=\"page-1\">\r\n        <PaginationLink\r\n          href=\"#\"\r\n          onClick={(e) => {\r\n            e.preventDefault();\r\n            handlePageChange(1);\r\n          }}\r\n          isActive={currentPage === 1}\r\n        >\r\n          1\r\n        </PaginationLink>\r\n      </PaginationItem>\r\n    );\r\n\r\n    // Show ellipsis if needed\r\n    if (currentPage > 3) {\r\n      items.push(\r\n        <PaginationItem key=\"ellipsis-1\">\r\n          <PaginationEllipsis />\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    // Show pages around current page\r\n    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {\r\n      if (i <= 1 || i >= totalPages) continue; // Skip first and last pages as they're always shown\r\n      items.push(\r\n        <PaginationItem key={`page-${i}`}>\r\n          <PaginationLink\r\n            href=\"#\"\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              handlePageChange(i);\r\n            }}\r\n            isActive={currentPage === i}\r\n          >\r\n            {i}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    // Show ellipsis if needed\r\n    if (currentPage < totalPages - 2) {\r\n      items.push(\r\n        <PaginationItem key=\"ellipsis-2\">\r\n          <PaginationEllipsis />\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    // Always show last page if there's more than one page\r\n    if (totalPages > 1) {\r\n      items.push(\r\n        <PaginationItem key={`page-${totalPages}`}>\r\n          <PaginationLink\r\n            href=\"#\"\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              handlePageChange(totalPages);\r\n            }}\r\n            isActive={currentPage === totalPages}\r\n          >\r\n            {totalPages}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    return items;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Sort Controls */}\r\n      <div className=\"flex justify-end mb-6\">\r\n        {/* Sort dropdown */}\r\n        <ReviewSortDropdown\r\n          sortBy={sortBy}\r\n          onSortChange={handleSortChange}\r\n          className=\"sm:w-auto\"\r\n        />\r\n      </div>\r\n\r\n\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <Alert variant=\"destructive\">\r\n          <AlertCircle className=\"h-4 w-4\" />\r\n          <AlertTitle>Error</AlertTitle>\r\n          <AlertDescription>{error}</AlertDescription>\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Loading state */}\r\n      {isLoading ? (\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n          {Array.from({ length: 4 }).map((_, index) => (\r\n            <ReviewCardSkeleton key={index} index={index} />\r\n          ))}\r\n        </div>\r\n      ) : reviews.length > 0 ? (\r\n        <>\r\n          <motion.div\r\n            variants={containerVariants}\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            className=\"grid grid-cols-1 md:grid-cols-2 gap-4\"\r\n          >\r\n            {reviews.map((review) => (\r\n              <ReviewCard\r\n                key={review.id}\r\n                review={review}\r\n                onDeleteSuccess={handleDeleteSuccess}\r\n              />\r\n            ))}\r\n          </motion.div>\r\n\r\n          {/* Pagination */}\r\n          {pagination.totalPages > 1 && (\r\n            <div className=\"mt-6\">\r\n              <Pagination>\r\n                <PaginationContent>\r\n                  {pagination.currentPage > 1 && (\r\n                    <PaginationItem>\r\n                      <motion.div\r\n                        whileHover={{ x: -2 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <PaginationPrevious\r\n                          href=\"#\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            handlePageChange(pagination.currentPage - 1);\r\n                          }}\r\n                          className=\"border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200\"\r\n                        />\r\n                      </motion.div>\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {generatePaginationItems().map((item, index) => (\r\n                    <motion.div\r\n                      key={`pagination-${index}`}\r\n                      initial={{ opacity: 0, scale: 0.8 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}\r\n                    >\r\n                      {item}\r\n                    </motion.div>\r\n                  ))}\r\n\r\n                  {pagination.currentPage < pagination.totalPages && (\r\n                    <PaginationItem>\r\n                      <motion.div\r\n                        whileHover={{ x: 2 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <PaginationNext\r\n                          href=\"#\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            handlePageChange(pagination.currentPage + 1);\r\n                          }}\r\n                          className=\"border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200\"\r\n                        />\r\n                      </motion.div>\r\n                    </PaginationItem>\r\n                  )}\r\n                </PaginationContent>\r\n              </Pagination>\r\n            </div>\r\n          )}\r\n        </>\r\n      ) : (\r\n        <Alert variant=\"default\" className=\"bg-white dark:bg-black border-neutral-200 dark:border-neutral-800\">\r\n          <AlertCircle className=\"h-4 w-4\" />\r\n          <AlertTitle>No reviews found</AlertTitle>\r\n          <AlertDescription>\r\n            You haven&apos;t written any reviews yet.\r\n          </AlertDescription>\r\n        </Alert>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;;;AAlBA;;;;;;;;;AA6Ce,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QAC3D,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,SAAS;IACX;IAIA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,6BAA6B;IAC7B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OAAO,MAAc;YACpD,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,cAAc,IAAI,gBAAgB;oBACtC,MAAM,KAAK,QAAQ;oBACnB,MAAM;gBACR;gBAEA,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,YAAY,QAAQ,IAAI;gBAE9E,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO;gBACvB,cAAc,KAAK,UAAU;YAC/B,EAAE,OAAO,MAAM;gBACb,SAAS;gBACT,WAAW,EAAE;YACf,SAAU;gBACR,aAAa;YACf;QACF;6DAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,aAAa,WAAW,WAAW,EAAE;QACvC;6CAAG;QAAC,WAAW,WAAW;QAAE;QAAQ;KAAa;IAEjD,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAK,CAAC;IACvD;IAEA,yBAAyB;IACzB,MAAM,sBAAsB,CAAC;QAC3B,WAAW,CAAA,cAAe,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAE3D,iEAAiE;QACjE,IAAI,QAAQ,MAAM,KAAK,KAAK,WAAW,WAAW,GAAG,GAAG;YACtD,iBAAiB,WAAW,WAAW,GAAG;QAC5C,OAAO;YACL,2BAA2B;YAC3B,aAAa,WAAW,WAAW,EAAE;QACvC;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,UAAU;QACV,2CAA2C;QAC3C,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAE,CAAC;IACpD;IAIA,4BAA4B;IAC5B,MAAM,0BAA0B;QAC9B,MAAM,QAAQ,EAAE;QAChB,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG;QAEpC,yBAAyB;QACzB,MAAM,IAAI,eACR,6LAAC,kIAAA,CAAA,iBAAc;sBACb,cAAA,6LAAC,kIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,iBAAiB;gBACnB;gBACA,UAAU,gBAAgB;0BAC3B;;;;;;WARiB;;;;;QActB,0BAA0B;QAC1B,IAAI,cAAc,GAAG;YACnB,MAAM,IAAI,eACR,6LAAC,kIAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,kIAAA,CAAA,qBAAkB;;;;;eADD;;;;;QAIxB;QAEA,iCAAiC;QACjC,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,IAAI,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,IAAI,IAAK;YAC9F,IAAI,KAAK,KAAK,KAAK,YAAY,UAAU,oDAAoD;YAC7F,MAAM,IAAI,eACR,6LAAC,kIAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,kIAAA,CAAA,iBAAc;oBACb,MAAK;oBACL,SAAS,CAAC;wBACR,EAAE,cAAc;wBAChB,iBAAiB;oBACnB;oBACA,UAAU,gBAAgB;8BAEzB;;;;;;eATgB,CAAC,KAAK,EAAE,GAAG;;;;;QAapC;QAEA,0BAA0B;QAC1B,IAAI,cAAc,aAAa,GAAG;YAChC,MAAM,IAAI,eACR,6LAAC,kIAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,kIAAA,CAAA,qBAAkB;;;;;eADD;;;;;QAIxB;QAEA,sDAAsD;QACtD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,eACR,6LAAC,kIAAA,CAAA,iBAAc;0BACb,cAAA,6LAAC,kIAAA,CAAA,iBAAc;oBACb,MAAK;oBACL,SAAS,CAAC;wBACR,EAAE,cAAc;wBAChB,iBAAiB;oBACnB;oBACA,UAAU,gBAAgB;8BAEzB;;;;;;eATgB,CAAC,KAAK,EAAE,YAAY;;;;;QAa7C;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,gKAAA,CAAA,UAAkB;oBACjB,QAAQ;oBACR,cAAc;oBACd,WAAU;;;;;;;;;;;YAOb,uBACC,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,6HAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,6LAAC,6HAAA,CAAA,mBAAgB;kCAAE;;;;;;;;;;;;YAKtB,0BACC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,gKAAA,CAAA,UAAkB;wBAAa,OAAO;uBAAd;;;;;;;;;uBAG3B,QAAQ,MAAM,GAAG,kBACnB;;kCACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,WAAU;kCAET,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,wJAAA,CAAA,UAAU;gCAET,QAAQ;gCACR,iBAAiB;+BAFZ,OAAO,EAAE;;;;;;;;;;oBAQnB,WAAW,UAAU,GAAG,mBACvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,kIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,kIAAA,CAAA,oBAAiB;;oCACf,WAAW,WAAW,GAAG,mBACxB,6LAAC,kIAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,GAAG,CAAC;4CAAE;4CACpB,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,kIAAA,CAAA,qBAAkB;gDACjB,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,iBAAiB,WAAW,WAAW,GAAG;gDAC5C;gDACA,WAAU;;;;;;;;;;;;;;;;oCAMjB,0BAA0B,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAK;sDAEtD;2CALI,CAAC,WAAW,EAAE,OAAO;;;;;oCAS7B,WAAW,WAAW,GAAG,WAAW,UAAU,kBAC7C,6LAAC,kIAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,GAAG;4CAAE;4CACnB,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,kIAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,iBAAiB,WAAW,WAAW,GAAG;gDAC5C;gDACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6CAW5B,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;kCACjC,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,6HAAA,CAAA,aAAU;kCAAC;;;;;;kCACZ,6LAAC,6HAAA,CAAA,mBAAgB;kCAAC;;;;;;;;;;;;;;;;;;AAO5B;GAtRwB;KAAA", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/customer/reviews/components/ReviewsPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Star } from \"lucide-react\";\r\nimport EnhancedReviewListClient from \"./EnhancedReviewListClient\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\n\r\ninterface ReviewsPageClientProps {\r\n  reviewsCount: number;\r\n}\r\n\r\nexport default function ReviewsPageClient({ reviewsCount }: ReviewsPageClientProps) {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header Section with proper layout */}\r\n      <div className=\"flex items-center gap-4\">\r\n        <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n          <Star className=\"w-6 h-6 text-amber-600 dark:text-amber-400\" />\r\n        </div>\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-foreground\">\r\n            Your Reviews\r\n          </h1>\r\n          <p className=\"text-muted-foreground mt-1\">\r\n            {formatIndianNumberShort(reviewsCount)} {reviewsCount === 1 ? 'review' : 'reviews'} you&apos;ve written for businesses\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content Section */}\r\n      <div className=\"space-y-6\">\r\n        {/* Enhanced Review List - now handles its own data fetching */}\r\n        <EnhancedReviewListClient />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,kBAAkB,EAAE,YAAY,EAA0B;IAChF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;;oCACV,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;oCAAc;oCAAE,iBAAiB,IAAI,WAAW;oCAAU;;;;;;;;;;;;;;;;;;;0BAMzF,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,sMAAA,CAAA,UAAwB;;;;;;;;;;;;;;;;AAIjC;KAzBwB", "debugId": null}}]}